import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useMediaQuery,
  useTheme,
  Paper
} from '@mui/material';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import styled from 'styled-components';
import { FaList } from 'react-icons/fa';
import MainCard from '../../ui-component/cards/MainCard';

// Design system
import { COLORS, TYPOGRAPHY, SPACING } from '../../themes/designSystem';

// Styled components for responsive design using Material-UI Box
const ResponsiveContainer = styled(Box)`
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
`;

const PromotionCard = styled(Paper)`
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  border: none;
  border-radius: 12px;
  padding: 2rem;

  @media (max-width: 768px) {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 8px;
  }
`;

const StepContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: center;
  }
`;

const Step = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  padding: 0 1rem;

  @media (max-width: 576px) {
    margin-bottom: 1rem;
  }
`;

const StepCircle = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  background-color: ${(props) => (props.active ? '#3a8dde' : '#e9ecef')};
  color: ${(props) => (props.active ? 'white' : '#6c757d')};
  font-weight: ${(props) => (props.active ? 'bold' : 'normal')};
`;

const StepConnector = styled.div`
  flex-grow: 1;
  height: 2px;
  background-color: ${(props) => (props.active ? '#3a8dde' : '#e9ecef')};
  margin: 0 -1rem;
  position: relative;
  top: 20px;
  z-index: 0;

  @media (max-width: 576px) {
    display: none;
  }
`;

const FormSection = styled.div`
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    margin-bottom: 1.5rem;
  }
`;

const DatePickerWrapper = styled.div`
  .react-datepicker-wrapper {
    width: 100%;
  }
`;

const GestionPromotions = () => {
  const API_URL = 'https://laravel-api.fly.dev/api';

  const colors = {
    primaryDark: '#2a3f5f',
    primaryLight: '#3a537b',
    accent: '#3a8dde',
    success: '#2ecc71',
    danger: '#e74c3c',
    warning: '#f39c12'
  };

  // États
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    nom: '',
    code: '',
    description: '',
    type: 'pourcentage',
    valeur: '',
    statut: 'active',
    date_debut: null,
    date_fin: null,
    priorite: 10,
    cumulable: false,
    produits: [],
    collections: [],
    profils_remise: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showPromotionsList, setShowPromotionsList] = useState(false);
  const [promotions, setPromotions] = useState([]);
  const [promotionsLoading, setPromotionsLoading] = useState(false);

  // Types et options
  const typesPromotion = [
    { value: 'pourcentage', label: 'Pourcentage' },
    { value: 'montant_fixe', label: 'Montant fixe' },
    { value: 'gratuit', label: 'Gratuit' }
  ];

  const statutsPromotion = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'programmee', label: 'Programmée' }
  ];

  // Nombre total d'étapes
  const totalSteps = 3;

  // Gestion des changements
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Gestion des dates
  const handleDateChange = (date, field) => {
    setFormData({
      ...formData,
      [field]: date
    });
  };

  // Navigation entre étapes
  const nextStep = () => {
    if (currentStep === 1 && (!formData.nom || !formData.type || !formData.valeur)) {
      setError('Veuillez remplir tous les champs obligatoires');
      return;
    }

    setError(null);
    setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  // Soumission du formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const payload = {
        ...formData,
        date_debut: formData.date_debut ? formData.date_debut.toISOString() : null,
        date_fin: formData.date_fin ? formData.date_fin.toISOString() : null
      };

      const response = await fetch(`${API_URL}/promotions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erreur lors de l'ajout");
      }

      setSuccess('Promotion créée avec succès!');
      setFormData({
        nom: '',
        code: '',
        description: '',
        type: 'pourcentage',
        valeur: '',
        statut: 'active',
        date_debut: null,
        date_fin: null,
        priorite: 10,
        cumulable: false,
        produits: [],
        collections: [],
        profils_remise: []
      });
      setCurrentStep(1);
    } catch (err) {
      console.error('Erreur:', err);
      setError(err.message || 'Erreur lors de la création de la promotion');
    } finally {
      setLoading(false);
    }
  };

  // Charger les promotions
  const fetchPromotions = async () => {
    try {
      setPromotionsLoading(true);
      const response = await fetch(`${API_URL}/promotions`);
      if (!response.ok) throw new Error('Erreur de chargement');
      const data = await response.json();
      const promotionsData = data.data || data;
      setPromotions(Array.isArray(promotionsData) ? promotionsData : []);
      setPromotionsLoading(false);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de charger la liste des promotions');
      setPromotions([]);
      setPromotionsLoading(false);
    }
  };

  // Barre de progression personnalisée
  const CustomProgressBar = () => {
    return (
      <StepContainer>
        {[...Array(totalSteps)].map((_, i) => (
          <React.Fragment key={i}>
            <Step>
              <StepCircle active={currentStep > i + 1 || currentStep === i + 1}>{currentStep > i + 1 ? '✓' : i + 1}</StepCircle>
              <Typography variant="caption" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                {i === 0 && 'Informations'}
                {i === 1 && 'Cibles'}
                {i === 2 && 'Confirmation'}
              </Typography>
            </Step>
            {i < totalSteps - 1 && <StepConnector active={currentStep > i + 1} />}
          </React.Fragment>
        ))}
      </StepContainer>
    );
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <ResponsiveContainer>
      {/* Titre principal */}
      <Box sx={{ textAlign: 'center', mb: { xs: 3, md: 5 } }}>
        <Typography
          variant="h3"
          component="h1"
          sx={{
            fontSize: { xs: '1.8rem', md: '2.2rem' },
            fontWeight: 600,
            color: colors.primaryDark,
            letterSpacing: '1px',
            mb: 2,
            fontFamily: TYPOGRAPHY.fontFamily.primary
          }}
        >
          GESTION DES PROMOTIONS
        </Typography>
        <Box
          sx={{
            height: '3px',
            width: '120px',
            background: 'linear-gradient(90deg, rgba(58,83,155,0.2) 0%, rgba(58,83,155,1) 50%, rgba(58,83,155,0.2) 100%)',
            borderRadius: '3px',
            margin: '0 auto'
          }}
        />
      </Box>

      {/* Messages d'alerte */}
      {error && (
        <Alert severity="error" onClose={() => setError(null)} sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" onClose={() => setSuccess(null)} sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      {/* Carte principale */}
      <PromotionCard>
        <Box component="form" onSubmit={handleSubmit}>
          <CustomProgressBar />

          {/* Étape 1: Informations de base */}
          {currentStep === 1 && (
            <FormSection>
              <Typography
                variant="h5"
                sx={{
                  mb: 3,
                  color: COLORS.primary.main,
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  fontSize: { xs: TYPOGRAPHY.fontSize.lg, md: TYPOGRAPHY.fontSize.xl }
                }}
              >
                Informations de la promotion
              </Typography>
              <Grid container spacing={{ xs: 2, md: 3 }}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Nom de la promotion"
                    name="nom"
                    value={formData.nom}
                    onChange={handleChange}
                    placeholder="Nom de la promotion"
                    required
                    size={isMobile ? 'small' : 'medium'}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Code promotionnel"
                    name="code"
                    value={formData.code}
                    onChange={handleChange}
                    placeholder="Code unique (optionnel)"
                    size={isMobile ? 'small' : 'medium'}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Description de la promotion"
                    multiline
                    rows={isMobile ? 2 : 3}
                    size={isMobile ? 'small' : 'medium'}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth required size={isMobile ? 'small' : 'medium'}>
                    <InputLabel sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>Type de promotion</InputLabel>
                    <Select name="type" value={formData.type} onChange={handleChange} label="Type de promotion">
                      {typesPromotion.map((type) => (
                        <MenuItem key={type.value} value={type.value}>
                          {type.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Valeur"
                    name="valeur"
                    type="number"
                    value={formData.valeur}
                    onChange={handleChange}
                    placeholder={formData.type === 'pourcentage' ? '10' : '5'}
                    required
                    size={isMobile ? 'small' : 'medium'}
                    inputProps={{
                      min: 0,
                      step: formData.type === 'pourcentage' ? 0.1 : 0.01
                    }}
                    helperText={
                      formData.type === 'pourcentage'
                        ? 'Pourcentage de réduction'
                        : formData.type === 'montant_fixe'
                          ? 'Montant fixe de réduction'
                          : 'Produit gratuit'
                    }
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      },
                      '& .MuiFormHelperText-root': {
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth required size={isMobile ? 'small' : 'medium'}>
                    <InputLabel sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>Statut</InputLabel>
                    <Select name="statut" value={formData.statut} onChange={handleChange} label="Statut">
                      {statutsPromotion.map((statut) => (
                        <MenuItem key={statut.value} value={statut.value}>
                          {statut.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Navigation Buttons */}
                <Grid item xs={12}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: 2,
                      mt: 3,
                      flexDirection: { xs: 'column', sm: 'row' }
                    }}
                  >
                    <Button
                      variant="contained"
                      onClick={nextStep}
                      disabled={loading}
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        width: { xs: '100%', sm: 'auto' },
                        minWidth: { sm: '120px' }
                      }}
                    >
                      Suivant
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </FormSection>
          )}

          {/* Placeholder for other steps */}
          {currentStep === 2 && (
            <FormSection>
              <Typography
                variant="h5"
                sx={{
                  mb: 3,
                  color: COLORS.primary.main,
                  fontFamily: TYPOGRAPHY.fontFamily.primary
                }}
              >
                Étape 2 - Cibles de la promotion
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                Cette étape sera implémentée prochainement avec Material-UI pour la sélection des produits et collections.
              </Alert>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3, flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                <Button variant="outlined" onClick={prevStep} sx={{ width: { xs: '100%', sm: 'auto' } }}>
                  Précédent
                </Button>
                <Button variant="contained" onClick={nextStep} sx={{ width: { xs: '100%', sm: 'auto' } }}>
                  Suivant
                </Button>
              </Box>
            </FormSection>
          )}

          {currentStep === 3 && (
            <FormSection>
              <Typography
                variant="h5"
                sx={{
                  mb: 3,
                  color: COLORS.primary.main,
                  fontFamily: TYPOGRAPHY.fontFamily.primary
                }}
              >
                Étape 3 - Confirmation
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                Récapitulatif des informations saisies. Cette étape sera complétée avec l'affichage détaillé des données.
              </Alert>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3, flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                <Button variant="outlined" onClick={prevStep} sx={{ width: { xs: '100%', sm: 'auto' } }}>
                  Précédent
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                  sx={{ width: { xs: '100%', sm: 'auto' } }}
                >
                  {loading ? 'Enregistrement...' : 'Confirmer et créer'}
                </Button>
              </Box>
            </FormSection>
          )}
        </Box>
      </PromotionCard>

      {/* Bouton pour voir la liste des promotions */}
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Button
          variant="outlined"
          onClick={() => {
            fetchPromotions();
            setShowPromotionsList(true);
          }}
          startIcon={<FaList />}
          sx={{
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            width: { xs: '100%', sm: 'auto' },
            minWidth: { sm: '200px' }
          }}
        >
          Voir la liste des promotions
        </Button>
      </Box>
    </ResponsiveContainer>
  );
};

export default GestionPromotions;
