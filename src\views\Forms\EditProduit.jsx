import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, Card, Form, Button, Row, Col, Al<PERSON>, Spinner, ListGroup, Badge, Tab, Nav, Table, InputGroup } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';
import { FormModal, ConfirmationModal, InfoModal } from '../../ui-component/extended/ModalVariants';
import { FaEdit, FaCheck, FaSave } from 'react-icons/fa';
import ProductStep1 from './ProductStep1';
import ProductStep2 from './ProductStep2';
import ProductStep3 from './ProductStep3';
import ProductStep4 from './ProductStep4';
import ProductStepVariants from './ProductStepVariants';
import { fetchCategories, fetchSousCategories, fetchSousSousCategories } from '../../services/categoryService';
import { fetchProductAttributs, fetchSousSousCategorieAttributs, fetchProductVariantes } from '../../services/attributService';

const EditProduit = ({ product, onClose, onSuccess }) => {
  // Use proxy in development, direct URL in production
  const API_URL = import.meta.env.DEV ? '/api' : import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';
  const [formData, setFormData] = useState({ ...product });
  const [categories, setCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [sousCategoriesLoading, setSousCategoriesLoading] = useState(false);
  const [sousSousCategoriesLoading, setSousSousCategoriesLoading] = useState(false);
  const [marques, setMarques] = useState([]);
  const [attributs, setAttributs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedAttributs, setSelectedAttributs] = useState(product.attributs || []);
  const [attributValues, setAttributValues] = useState(product.attributValues || {});
  const [images, setImages] = useState(product.images ? [...product.images] : []); // Existing images + new uploads
  const [primaryImageIndex, setPrimaryImageIndex] = useState(() => {
    if (product.images && product.images.length > 0) {
      const idx = product.images.findIndex((img) => img.is_primary);
      return idx >= 0 ? idx : 0;
    }
    return 0;
  });
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [variantes, setVariantes] = useState(product.variantes || []);
  const [showVarianteModal, setShowVarianteModal] = useState(false);
  const [newVariante, setNewVariante] = useState({ sku: '', prix_supplement: 0, quantite: 0, image: null, valeurs: {} });
  const [varianteImagePreview, setVarianteImagePreview] = useState(null);
  const totalSteps = 5;

  // Fetch attributs and variantes for product and sous-sous-categorie
  useEffect(() => {
    const fetchAttributsAndVariantes = async () => {
      try {
        // Fetch attributs for product
        if (product.id) {
          const prodAttrRes = await fetchProductAttributs(product.id);
          setAttributs(prodAttrRes.data || prodAttrRes || []);
        }
        // Fetch attributs for sous-sous-categorie if available
        const sousSousCatId = formData.sous_sous_categorie_id || product.sous_sous_categorie_id;
        if (sousSousCatId) {
          const sousSousAttrRes = await fetchSousSousCategorieAttributs(sousSousCatId);
          // Optionally merge with product attributs if needed
          setAttributs((prev) =>
            Array.isArray(prev)
              ? [...prev, ...(sousSousAttrRes.data || sousSousAttrRes || [])]
              : sousSousAttrRes.data || sousSousAttrRes || []
          );
        }
        // Fetch variants for product
        if (product.id) {
          const variantesRes = await fetchProductVariantes(product.id);
          setVariantes(variantesRes.data || variantesRes || []);
        }
      } catch (err) {
        // Optionally set error
      }
    };
    fetchAttributsAndVariantes();
  }, [product.id, formData.sous_sous_categorie_id]);

  // Automatically determine and set the correct category and sous-category based on sous_sous_categorie_id
  useEffect(() => {
    const preselectCategoryHierarchy = async () => {
      if (product.sous_sous_categorie_id && categories.length > 0) {
        // Fetch all sous-sous-categories for the current sous_categorie (or all if needed)
        let allSousSousCats = [];
        try {
          // Try to fetch all sous-sous-categories for all sous-categories (for robustness)
          // If you have an endpoint for all, use it; otherwise, fetch for each sous-categorie
          // Here, we assume you can fetch for each sous-categorie in categories
          for (const cat of categories) {
            const sousCatsData = await fetchSousCategories(cat.id);
            const sousCats = sousCatsData.data || sousCatsData || [];
            for (const sousCat of sousCats) {
              const sousSousCatsData = await fetchSousSousCategories(sousCat.id);
              const sousSousCats = sousSousCatsData.data || sousSousCatsData || [];
              allSousSousCats = allSousSousCats.concat(sousSousCats);
            }
          }
        } catch (err) {
          // fallback: leave allSousSousCats empty
        }
        const sousSousCat = allSousSousCats.find((ssc) => ssc.id === product.sous_sous_categorie_id);
        if (sousSousCat) {
          const sous_categorie_id = sousSousCat.sous_categorie_id;
          // Now find the sous-categorie and its categorie_id
          let matchedSousCat = null;
          let matchedCategorieId = null;
          for (const cat of categories) {
            const sousCatsData = await fetchSousCategories(cat.id);
            const sousCats = sousCatsData.data || sousCatsData || [];
            const foundSousCat = sousCats.find((sc) => sc.id === sous_categorie_id);
            if (foundSousCat) {
              matchedSousCat = foundSousCat;
              matchedCategorieId = cat.id;
              break;
            }
          }
          if (matchedSousCat && matchedCategorieId) {
            setFormData((prev) => ({
              ...prev,
              categorie_id: matchedCategorieId,
              sous_categorie_id: matchedSousCat.id,
              sous_sous_categorie_id: product.sous_sous_categorie_id
            }));
          }
        }
      }
    };
    preselectCategoryHierarchy();
  }, [categories, product.sous_sous_categorie_id]);

  // Fetch sousCategories when the selected category changes
  useEffect(() => {
    const fetchSousCats = async () => {
      const catId = formData.categorie_id || product.categorie_id;
      if (catId) {
        try {
          setSousCategoriesLoading(true);
          const sousCatsData = await fetchSousCategories(catId);
          setSousCategories(sousCatsData.data || sousCatsData || []);
          setSousCategoriesLoading(false);
        } catch (err) {
          setSousCategories([]);
        }
      } else {
        setSousCategories([]);
      }
    };
    fetchSousCats();
  }, [formData.categorie_id, product.categorie_id]);

  // Fetch sousSousCategories when the selected sous-categorie changes
  useEffect(() => {
    const fetchSousSousCats = async () => {
      const sousCatId = formData.sous_categorie_id || product.sous_categorie_id;
      if (sousCatId) {
        try {
          setSousSousCategoriesLoading(true);
          const sousSousCatsData = await fetchSousSousCategories(sousCatId);
          setSousSousCategories(sousSousCatsData.data || sousSousCatsData || []);
          setSousSousCategoriesLoading(false);
        } catch (err) {
          setSousSousCategories([]);
        }
      } else {
        setSousSousCategories([]);
      }
    };
    fetchSousSousCats();
  }, [formData.sous_categorie_id, product.sous_categorie_id]);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setInitialLoading(true);
        console.log('🔄 Fetching initial data from:', API_URL);

        // Test API connection first
        try {
          const testResponse = await fetch(`${API_URL}/marques`);
          if (!testResponse.ok) {
            throw new Error(`API connection failed: ${testResponse.status}`);
          }
        } catch (testError) {
          console.error('❌ API connection test failed:', testError);
          if (testError.message.includes('fetch')) {
            setError('Impossible de se connecter au serveur API. Vérifiez que le serveur est en ligne.');
          } else {
            setError('Le serveur API est temporairement indisponible.');
          }
          setInitialLoading(false);
          return;
        }

        // Fetch marques
        const marquesRes = await fetch(`${API_URL}/marques`);
        const marquesData = await marquesRes.json();
        setMarques(marquesData.data || marquesData || []);

        // Fetch categories using service
        setCategoriesLoading(true);
        const categoriesData = await fetchCategories();
        setCategories(categoriesData.data || categoriesData || []);
        setCategoriesLoading(false);
      } catch (err) {
        console.error('❌ Error fetching initial data:', err);
        setError('Erreur de chargement des données initiales: ' + err.message);
      } finally {
        setInitialLoading(false);
      }
    };
    fetchInitialData();
  }, [product]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  // Handle multiple image selection (append new images)
  const handleImagesChange = (e) => {
    const files = Array.from(e.target.files);
    // Only add files that are not already present (avoid duplicates by name)
    const newImages = files.filter((f) => !images.some((img) => (img instanceof File ? img.name : img.id) === f.name));
    setImages((prev) => [...prev, ...newImages]);
    // If no primary selected, set the first as primary
    if (images.length === 0 && newImages.length > 0) setPrimaryImageIndex(0);
  };

  // Set a specific image as primary
  const handleSetPrimaryImage = (index) => {
    setPrimaryImageIndex(index);
  };

  // Remove image
  const handleRemoveImage = (idx) => {
    setImages((prev) => prev.filter((_, i) => i !== idx));
    if (primaryImageIndex === idx) setPrimaryImageIndex(0);
    else if (primaryImageIndex > idx) setPrimaryImageIndex(primaryImageIndex - 1);
  };

  const submitEditForm = async () => {
    setShowConfirmModal(false);
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      // 1. Update product info
      console.log('🔄 Updating product with ID:', product.id);
      console.log('📝 Form data to send:', formData);
      console.log('🌐 API URL:', API_URL);

      // Use JSON for product updates instead of FormData for better compatibility
      const response = await fetch(`${API_URL}/produits/${product.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json'
        },
        body: JSON.stringify(formData)
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
          // Check if it's a network error
          if (response.status === 502 || response.status === 0) {
            throw new Error('Le serveur API est temporairement indisponible. Veuillez réessayer plus tard.');
          }
          throw new Error(`Erreur HTTP ${response.status}: ${response.statusText}`);
        }
        console.error('❌ Update failed with error:', errorData);
        throw new Error(errorData.message || 'Erreur lors de la mise à jour du produit');
      }

      const updateResult = await response.json();
      console.log('✅ Product updated successfully:', updateResult);

      // 2. Upload new images if any (Files only)
      const existingImages = images.filter((img) => !(img instanceof File));
      const newImages = images.filter((img) => img instanceof File);
      if (newImages.length > 0) {
        const imagesFormData = new FormData();
        newImages.forEach((img, idx) => {
          imagesFormData.append('images[]', img);
        });
        imagesFormData.append('model_type', 'produit');
        imagesFormData.append('model_id', product.id);
        // Mark the primary image index among ALL images
        imagesFormData.append('primary_index', primaryImageIndex);
        const imgResponse = await fetch(`${API_URL}/images/upload-multiple`, {
          method: 'POST',
          body: imagesFormData
        });
        if (!imgResponse.ok) {
          let imgError;
          try {
            imgError = await imgResponse.json();
          } catch (parseError) {
            if (imgResponse.status === 502 || imgResponse.status === 0) {
              throw new Error("Le serveur API est temporairement indisponible pour l'upload des images. Veuillez réessayer plus tard.");
            }
            throw new Error(`Erreur lors de l'upload des images: ${imgResponse.status} ${imgResponse.statusText}`);
          }
          throw new Error(imgError.message || "Erreur lors de l'upload des images");
        }
      } else if (existingImages.length > 0) {
        // If only existing images, update primary via PATCH
        await fetch(`${API_URL}/images/set-primary`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            model_type: 'produit',
            model_id: product.id,
            image_id: existingImages[primaryImageIndex]?.id
          })
        });
      }

      setSuccess(true);
      setShowSuccessModal(true);
      if (onSuccess) onSuccess();
    } catch (err) {
      console.error('❌ Error in submitEditForm:', err);
      // Handle network errors specifically
      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        setError('Impossible de se connecter au serveur. Vérifiez votre connexion internet et réessayez.');
      } else {
        setError(err.message || "Une erreur inattendue s'est produite.");
      }
    } finally {
      setLoading(false);
    }
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const nextStep = () => {
    setCurrentStep(currentStep + 1);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('🔄 handleSubmit called, currentStep:', currentStep, 'totalSteps:', totalSteps);

    if (currentStep === totalSteps) {
      // Prevent submission if no image is selected
      if (images.length === 0) {
        setError('Veuillez sélectionner au moins une image pour le produit.');
        console.log('❌ No images selected');
        return;
      }
      console.log('✅ Calling submitEditForm');
      submitEditForm();
    } else {
      console.log('➡️ Moving to next step');
      nextStep();
    }
  };

  return (
    <>
      <FormModal
        show={true}
        onHide={onClose}
        onSubmit={handleSubmit}
        title={`Modifier le produit - ${product.nom_produit}`}
        isEdit={true}
        loading={loading && !initialLoading}
        size="lg"
        submitText="Sauvegarder les modifications"
        cancelText="Annuler"
        icon={<FaEdit />}
        backdrop="static"
      >
        <Form onSubmit={handleSubmit}>
            {/* Initial Loading Display */}
            {initialLoading && (
              <div className="text-center py-5">
                <Spinner animation="border" variant="primary" className="mb-3" />
                <h5>Chargement des données...</h5>
                <p className="text-muted mb-0">Veuillez patienter pendant le chargement des informations du produit.</p>
              </div>
            )}

            {/* Error Display */}
            {error && !initialLoading && (
              <Alert variant="danger" className="mb-3">
                <i className="fas fa-exclamation-triangle me-2"></i>
                {error}
              </Alert>
            )}

            {/* Main Content - Only show when not initially loading */}
            {!initialLoading && (
              <>
                {/* Barre de progression */}
                <div className="mb-2 p-2 bg-light rounded-2">
                  <div className="d-flex align-items-center justify-content-between">
                    {[1, 2, 3, 4, 5].map((step) => (
                      <React.Fragment key={step}>
                        <div
                          className={`d-flex flex-column align-items-center progress-step ${
                            currentStep > step ? 'text-success' : currentStep === step ? 'text-primary' : 'text-muted'
                          }`}
                          style={{ flex: '1', maxWidth: '100px' }}
                        >
                          <div
                            className={`rounded-circle d-flex align-items-center justify-content-center mb-2 fw-bold ${
                              currentStep > step
                                ? 'bg-success text-white'
                                : currentStep === step
                                  ? 'bg-primary text-white'
                                  : 'bg-white border border-2 text-muted'
                            }`}
                            style={{ width: '40px', height: '40px', fontSize: '1rem' }}
                          >
                            {currentStep > step ? <i className="fas fa-check"></i> : step}
                          </div>
                          <span className="small fw-medium text-center" style={{ fontSize: '0.85rem' }}>
                            {step === 1 && 'Informations'}
                            {step === 2 && 'Catégorie'}
                            {step === 3 && 'Attributs'}
                            {step === 4 && 'Images'}
                            {step === 5 && 'Variantes'}
                          </span>
                        </div>
                        {step < 5 && (
                          <div
                            className={`mx-2 ${currentStep > step ? 'bg-success' : currentStep === step ? 'bg-primary' : 'bg-secondary'}`}
                            style={{ height: '3px', flex: '1', borderRadius: '2px', opacity: currentStep > step ? 1 : 0.3 }}
                          ></div>
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                </div>

                {currentStep === 1 && <ProductStep1 formData={formData} setFormData={setFormData} error={error} />}
                {currentStep === 2 && (
                  <ProductStep2
                    formData={formData}
                    setFormData={setFormData}
                    brands={marques}
                    categories={categories}
                    sousCategories={sousCategories}
                    sousSousCategories={sousSousCategories}
                    categoriesLoading={categoriesLoading}
                    sousCategoriesLoading={sousCategoriesLoading}
                    sousSousCategoriesLoading={sousSousCategoriesLoading}
                    error={error}
                  />
                )}
                {currentStep === 3 && (
                  <ProductStep3
                    formData={formData}
                    setFormData={setFormData}
                    attributFields={attributs}
                    attributValues={attributValues}
                    setAttributValues={setAttributValues}
                    error={error}
                  />
                )}
                {currentStep === 4 && (
                  <div>
                    <Form.Group controlId="productImages">
                      <Form.Label>
                        Images du produit <span style={{ color: 'red' }}>*</span>
                      </Form.Label>
                      <Form.Control type="file" multiple accept="image/*" onChange={handleImagesChange} />
                      <Form.Text className="text-muted">
                        Veuillez sélectionner au moins une image. Cliquez sur une image pour la définir comme principale.
                      </Form.Text>
                      <div className="d-flex mt-2 flex-wrap">
                        {images.map((img, idx) => (
                          <div
                            key={idx}
                            className={`m-2 border ${idx === primaryImageIndex ? 'border-primary' : ''}`}
                            style={{ cursor: 'pointer', width: 80, height: 80, position: 'relative' }}
                          >
                            <img
                              src={img instanceof File ? URL.createObjectURL(img) : img.url}
                              alt={img.name || img.filename || 'image'}
                              style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 4 }}
                              onClick={() => handleSetPrimaryImage(idx)}
                            />
                            {idx === primaryImageIndex && (
                              <Badge bg="primary" style={{ position: 'absolute', top: 0, left: 0 }}>
                                Principale
                              </Badge>
                            )}
                            <Button
                              variant="danger"
                              size="sm"
                              style={{ position: 'absolute', top: 0, right: 0, borderRadius: '50%' }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveImage(idx);
                              }}
                            >
                              &times;
                            </Button>
                          </div>
                        ))}
                      </div>
                    </Form.Group>
                  </div>
                )}
                {currentStep === 5 && (
                  <ProductStepVariants
                    variantes={variantes}
                    setVariantes={setVariantes}
                    newVariante={newVariante}
                    setNewVariante={setNewVariante}
                    showVarianteModal={showVarianteModal}
                    setShowVarianteModal={setShowVarianteModal}
                    handleVarianteImageChange={() => {}}
                    varianteImagePreview={varianteImagePreview}
                    setVarianteImagePreview={setVarianteImagePreview}
                    attributs={attributs}
                    handleVarianteAttributChange={() => {}}
                    addVariante={() => {}}
                    removeVariante={() => {}}
                    error={error}
                  />
                )}
              </>
            )}
          </Modal.Body>

          <Modal.Footer className="professional-modal-footer">
            {!initialLoading ? (
              <div className="d-flex justify-content-between align-items-center w-100">
                <div>
                  {currentStep > 1 && (
                    <Button variant="outline-secondary" onClick={prevStep} disabled={loading}>
                      <i className="fas fa-arrow-left me-1"></i>
                      Précédent
                    </Button>
                  )}
                </div>

                <div className="text-center">
                  <small className="text-muted">
                    Étape {currentStep} sur {totalSteps}
                  </small>
                </div>

                <div className="d-flex gap-2">
                  <Button variant="outline-danger" onClick={onClose} disabled={loading}>
                    <i className="fas fa-times me-1"></i>
                    Annuler
                  </Button>
                  {currentStep < totalSteps && (
                    <Button variant="primary" onClick={nextStep} disabled={loading}>
                      Suivant
                      <i className="fas fa-arrow-right ms-1"></i>
                    </Button>
                  )}
                  {currentStep === totalSteps && (
                    <Button
                      variant="success"
                      type="submit"
                      disabled={loading}
                      onClick={(e) => {
                        console.log('🔘 Enregistrer button clicked');
                        handleSubmit(e);
                      }}
                    >
                      {loading ? (
                        <>
                          <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-1" />
                          Enregistrement...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-save me-1"></i> Enregistrer
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="d-flex justify-content-end">
                <Button variant="outline-danger" onClick={onClose}>
                  <i className="fas fa-times me-1"></i>
                  Fermer
                </Button>
              </div>
            )}
        </Form>
      </FormModal>

      {/* Professional Loading Modal */}
      <InfoModal
        show={loading && !initialLoading}
        title="Mise à jour en cours..."
        showFooter={false}
        backdrop="static"
        keyboard={false}
        icon={<FaSave />}
      >
        <div className="text-center py-4">
          <Spinner animation="border" variant="primary" className="mb-3" />
          <h5>Mise à jour en cours...</h5>
          <p className="text-muted mb-0">Veuillez patienter pendant que nous sauvegardons les modifications.</p>
        </div>
      </InfoModal>

      {/* Professional Success Modal */}
      <InfoModal
        show={showSuccessModal}
        onHide={() => setShowSuccessModal(false)}
        title="Produit modifié avec succès !"
        showFooter={true}
        primaryText="Fermer"
        primaryAction={() => {
          setShowSuccessModal(false);
          onClose();
        }}
        variant="success"
        icon={<FaCheck />}
      >
        <div className="text-center py-4">
          <div className="mb-3">
            <i className="fas fa-check-circle text-success" style={{ fontSize: '3rem' }}></i>
          </div>
          <h5>Les modifications ont été sauvegardées !</h5>
          <p className="text-muted">Le produit a été mis à jour avec succès dans la base de données.</p>
        </div>
      </InfoModal>

      <style jsx global>{`
        /* Professional modal sizing is now handled by ProfessionalModal.css */

        .edit-product-modal .modal-body {
          min-height: 45vh;
          max-height: 65vh;
        }

        .progress-step {
          transition: all 0.3s ease;
        }

        .progress-step.active {
          transform: scale(1.05);
        }

        .step-content {
          min-height: 280px;
          padding: 0.5rem 0;
        }

        .form-group {
          margin-bottom: 1rem;
        }

        .form-control,
        .form-select {
          border-radius: 6px;
          border: 1px solid #e9ecef;
          padding: 0.5rem 0.75rem;
          font-size: 0.9rem;
          transition: all 0.3s ease;
        }

        .form-control:focus,
        .form-select:focus {
          border-color: #0d6efd;
          box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
        }

        .btn {
          border-radius: 6px;
          font-weight: 500;
          padding: 0.5rem 1rem;
          font-size: 0.9rem;
          transition: all 0.3s ease;
        }

        .btn:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Mobile responsive adjustments for progress steps */
        @media (max-width: 768px) {
          .step-content {
            min-height: 250px;
          }

          .progress-step {
            max-width: 70px;
          }

          .progress-step .rounded-circle {
            width: 32px;
            height: 32px;
            font-size: 0.8rem;
          }
        }

        @media (max-width: 576px) {
          .progress-step span {
            font-size: 0.7rem;
          }
        }
      `}</style>
    </>
  );
};

export default EditProduit;
