// ==============================|| DESIGN SYSTEM CONSTANTS ||============================== //

/**
 * Centralized design system for consistent styling across the application
 * Based on Material-UI theme with standardized colors, typography, and spacing
 */

// Modern Enhanced Colors - Professional and visually appealing
export const COLORS = {
  // Primary Colors - Modern Blue Gradient
  primary: {
    main: '#3b82f6',      // Modern blue
    light: '#dbeafe',     // Very light blue
    dark: '#1d4ed8',      // Deep blue
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a'
  },

  // Secondary Colors - Modern Purple
  secondary: {
    main: '#8b5cf6',      // Modern purple
    light: '#f3f4f6',     // Light grey-purple
    dark: '#7c3aed',      // Deep purple
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7',
    600: '#9333ea',
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87'
  },

  // Status Colors - Modern and vibrant
  success: {
    main: '#10b981',      // Modern green
    light: '#d1fae5',     // Light green
    dark: '#047857',      // Deep green
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981',
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b'
  },

  error: {
    main: '#ef4444',      // Modern red
    light: '#fecaca',     // Light red
    dark: '#dc2626',      // Deep red
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d'
  },

  warning: {
    main: '#f59e0b',      // Modern amber
    light: '#fef3c7',     // Light amber
    dark: '#d97706',      // Deep amber
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f'
  },

  info: {
    main: '#0ea5e9',      // Modern sky blue
    light: '#e0f2fe',     // Light sky blue
    dark: '#0284c7',      // Deep sky blue
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e'
  },

  // Modern Grey Scale - More refined
  grey: {
    50: '#f9fafb',       // Almost white
    100: '#f3f4f6',      // Very light grey
    200: '#e5e7eb',      // Light grey
    300: '#d1d5db',      // Medium light grey
    400: '#9ca3af',      // Medium grey
    500: '#6b7280',      // Medium dark grey
    600: '#4b5563',      // Dark grey
    700: '#374151',      // Very dark grey
    800: '#1f2937',      // Almost black
    900: '#111827'       // Near black
  },

  // Modern Text Colors
  text: {
    primary: '#1f2937',     // Dark grey for primary text
    secondary: '#6b7280',   // Medium grey for secondary text
    tertiary: '#9ca3af',    // Light grey for tertiary text
    dark: '#111827',        // Near black for emphasis
    light: '#f9fafb',       // Light for dark backgrounds
    hint: '#d1d5db'         // Very light for hints
  },

  // Modern Background Colors
  background: {
    paper: '#ffffff',       // Pure white for cards
    default: '#f9fafb',     // Very light grey for page background
    light: '#f3f4f6',       // Light grey for sections
    dark: '#1f2937',        // Dark for dark mode
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'  // Modern gradient
  },

  // Business Logic Colors
  business: {
    partner: '#e74c3c',
    loyal: '#f39c12',
    regular: '#2ecc71',
    architect: '#8e44ad',
    hotel: '#16a085',
    jline: '#3498db',
    location: '#27ae60'
  }
};

// Standard Typography
export const TYPOGRAPHY = {
  fontFamily: {
    primary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    secondary: "'Poppins', sans-serif",
    monospace: "'Roboto Mono', monospace"
  },

  fontSize: {
    xs: '0.75rem',      // 12px
    sm: '0.875rem',     // 14px
    base: '1rem',       // 16px
    lg: '1.125rem',     // 18px
    xl: '1.25rem',      // 20px
    '2xl': '1.5rem',    // 24px
    '3xl': '2.125rem'   // 34px
  },

  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  },

  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75
  }
};

// Standard Spacing
export const SPACING = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '1rem',       // 16px
  lg: '1.5rem',     // 24px
  xl: '2rem',       // 32px
  '2xl': '3rem',    // 48px
  '3xl': '4rem'     // 64px
};

// Responsive Breakpoints (following Material-UI standards)
export const BREAKPOINTS = {
  xs: 0,      // Extra small devices (phones)
  sm: 600,    // Small devices (tablets)
  md: 900,    // Medium devices (small laptops)
  lg: 1200,   // Large devices (desktops)
  xl: 1536    // Extra large devices (large desktops)
};

// Responsive Utilities
export const RESPONSIVE = {
  // Container max widths
  container: {
    xs: '100%',
    sm: '540px',
    md: '720px',
    lg: '960px',
    xl: '1140px'
  },

  // Responsive spacing
  spacing: {
    xs: {
      xs: '0.125rem',  // 2px
      sm: '0.25rem',   // 4px
      md: '0.5rem',    // 8px
      lg: '0.75rem',   // 12px
      xl: '1rem'       // 16px
    },
    sm: {
      xs: '0.25rem',   // 4px
      sm: '0.5rem',    // 8px
      md: '0.75rem',   // 12px
      lg: '1rem',      // 16px
      xl: '1.5rem'     // 24px
    },
    md: SPACING,       // Default spacing for medium and up
    lg: SPACING,
    xl: SPACING
  },

  // Responsive typography
  typography: {
    xs: {
      xs: '0.625rem',  // 10px
      sm: '0.75rem',   // 12px
      base: '0.875rem', // 14px
      lg: '1rem',      // 16px
      xl: '1.125rem'   // 18px
    },
    sm: {
      xs: '0.75rem',   // 12px
      sm: '0.875rem',  // 14px
      base: '1rem',    // 16px
      lg: '1.125rem',  // 18px
      xl: '1.25rem'    // 20px
    },
    md: TYPOGRAPHY.fontSize, // Default for medium and up
    lg: TYPOGRAPHY.fontSize,
    xl: TYPOGRAPHY.fontSize
  }
};

// Standard Border Radius
export const BORDER_RADIUS = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  full: '9999px'
};

// Modern Enhanced Shadows - More depth and sophistication
export const SHADOWS = {
  none: 'none',
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)',
  base: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
  md: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
  lg: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
  xl: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)',
  // Colored shadows for modern effect
  primary: '0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -4px rgba(59, 130, 246, 0.1)',
  success: '0 10px 15px -3px rgba(16, 185, 129, 0.1), 0 4px 6px -4px rgba(16, 185, 129, 0.1)',
  error: '0 10px 15px -3px rgba(239, 68, 68, 0.1), 0 4px 6px -4px rgba(239, 68, 68, 0.1)',
  warning: '0 10px 15px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -4px rgba(245, 158, 11, 0.1)'
};

// Modern Enhanced Button Variants
export const BUTTON_VARIANTS = {
  primary: {
    background: `linear-gradient(135deg, ${COLORS.primary.main} 0%, ${COLORS.primary[600]} 100%)`,
    color: '#ffffff',
    border: 'none',
    boxShadow: SHADOWS.primary,
    '&:hover': {
      background: `linear-gradient(135deg, ${COLORS.primary[600]} 0%, ${COLORS.primary[700]} 100%)`,
      boxShadow: SHADOWS.lg,
      transform: 'translateY(-1px)'
    },
    '&:active': {
      transform: 'translateY(0)',
      boxShadow: SHADOWS.base
    }
  },

  secondary: {
    background: `linear-gradient(135deg, ${COLORS.secondary.main} 0%, ${COLORS.secondary[600]} 100%)`,
    color: '#ffffff',
    border: 'none',
    boxShadow: SHADOWS.base,
    '&:hover': {
      background: `linear-gradient(135deg, ${COLORS.secondary[600]} 0%, ${COLORS.secondary[700]} 100%)`,
      boxShadow: SHADOWS.lg,
      transform: 'translateY(-1px)'
    }
  },

  success: {
    background: `linear-gradient(135deg, ${COLORS.success.main} 0%, ${COLORS.success[600]} 100%)`,
    color: '#ffffff',
    border: 'none',
    boxShadow: SHADOWS.success,
    '&:hover': {
      background: `linear-gradient(135deg, ${COLORS.success[600]} 0%, ${COLORS.success[700]} 100%)`,
      boxShadow: SHADOWS.lg,
      transform: 'translateY(-1px)'
    }
  },

  error: {
    background: `linear-gradient(135deg, ${COLORS.error.main} 0%, ${COLORS.error[600]} 100%)`,
    color: '#ffffff',
    border: 'none',
    boxShadow: SHADOWS.error,
    '&:hover': {
      background: `linear-gradient(135deg, ${COLORS.error[600]} 0%, ${COLORS.error[700]} 100%)`,
      boxShadow: SHADOWS.lg,
      transform: 'translateY(-1px)'
    }
  },

  warning: {
    background: `linear-gradient(135deg, ${COLORS.warning.main} 0%, ${COLORS.warning[600]} 100%)`,
    color: COLORS.text.dark,
    border: 'none',
    boxShadow: SHADOWS.warning,
    '&:hover': {
      background: `linear-gradient(135deg, ${COLORS.warning[600]} 0%, ${COLORS.warning[700]} 100%)`,
      color: '#ffffff',
      boxShadow: SHADOWS.lg,
      transform: 'translateY(-1px)'
    }
  },

  info: {
    background: `linear-gradient(135deg, ${COLORS.info.main} 0%, ${COLORS.info[600]} 100%)`,
    color: '#ffffff',
    border: 'none',
    boxShadow: SHADOWS.base,
    '&:hover': {
      background: `linear-gradient(135deg, ${COLORS.info[600]} 0%, ${COLORS.info[700]} 100%)`,
      boxShadow: SHADOWS.lg,
      transform: 'translateY(-1px)'
    }
  },

  outline: {
    backgroundColor: 'transparent',
    color: COLORS.primary.main,
    border: `2px solid ${COLORS.primary.main}`,
    boxShadow: 'none',
    '&:hover': {
      backgroundColor: COLORS.primary.main,
      color: '#ffffff',
      boxShadow: SHADOWS.primary,
      transform: 'translateY(-1px)'
    }
  },

  ghost: {
    backgroundColor: COLORS.grey[50],
    color: COLORS.text.primary,
    border: `1px solid ${COLORS.grey[200]}`,
    '&:hover': {
      backgroundColor: COLORS.grey[100],
      borderColor: COLORS.grey[300],
      boxShadow: SHADOWS.sm
    }
  }
};

// Modern Enhanced Table Styles
export const TABLE_STYLES = {
  container: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    boxShadow: SHADOWS.base,
    border: `1px solid ${COLORS.grey[200]}`,
    backgroundColor: COLORS.background.paper
  },

  header: {
    background: `linear-gradient(135deg, ${COLORS.grey[50]} 0%, ${COLORS.grey[100]} 100%)`,
    color: COLORS.text.dark,
    fontWeight: TYPOGRAPHY.fontWeight.semibold,
    fontSize: TYPOGRAPHY.fontSize.sm,
    padding: `${SPACING.lg} ${SPACING.md}`,
    borderBottom: `2px solid ${COLORS.grey[200]}`,
    textTransform: 'uppercase',
    letterSpacing: '0.05em'
  },

  cell: {
    padding: `${SPACING.md} ${SPACING.md}`,
    borderBottom: `1px solid ${COLORS.grey[100]}`,
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: COLORS.text.primary,
    transition: 'all 0.2s ease-in-out'
  },

  row: {
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: COLORS.primary[50],
      transform: 'scale(1.001)',
      boxShadow: SHADOWS.sm
    },
    '&:last-child td': {
      borderBottom: 'none'
    }
  },

  striped: {
    '&:nth-of-type(even)': {
      backgroundColor: COLORS.grey[25] || '#fafbfc'
    }
  },

  // Responsive table styles
  responsive: {
    // Mobile-first approach
    mobile: {
      container: {
        overflowX: 'auto',
        WebkitOverflowScrolling: 'touch',
        borderRadius: BORDER_RADIUS.md,
        margin: `0 -${SPACING.sm}`,
        padding: `0 ${SPACING.sm}`
      },
      table: {
        minWidth: '600px', // Minimum width for horizontal scroll
        fontSize: TYPOGRAPHY.fontSize.xs
      },
      header: {
        padding: `${SPACING.sm} ${SPACING.xs}`,
        fontSize: TYPOGRAPHY.fontSize.xs,
        position: 'sticky',
        top: 0,
        zIndex: 10,
        backgroundColor: COLORS.background.paper
      },
      cell: {
        padding: `${SPACING.sm} ${SPACING.xs}`,
        fontSize: TYPOGRAPHY.fontSize.xs,
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px'
      }
    },

    // Tablet styles
    tablet: {
      container: {
        overflowX: 'auto',
        borderRadius: BORDER_RADIUS.lg
      },
      table: {
        minWidth: '700px'
      },
      header: {
        padding: `${SPACING.md} ${SPACING.sm}`,
        fontSize: TYPOGRAPHY.fontSize.sm
      },
      cell: {
        padding: `${SPACING.md} ${SPACING.sm}`,
        fontSize: TYPOGRAPHY.fontSize.sm
      }
    },

    // Desktop styles (default)
    desktop: {
      container: {
        overflow: 'visible'
      },
      table: {
        minWidth: 'auto'
      }
    }
  }
};

// Standard Form Styles
export const FORM_STYLES = {
  input: {
    borderRadius: BORDER_RADIUS.base,
    border: `1px solid ${COLORS.grey[300]}`,
    padding: `${SPACING.sm} ${SPACING.md}`,
    fontSize: TYPOGRAPHY.fontSize.sm,
    '&:focus': {
      borderColor: COLORS.primary.main,
      outline: 'none',
      boxShadow: `0 0 0 3px ${COLORS.primary.light}`
    }
  },

  label: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    color: COLORS.text.dark,
    marginBottom: SPACING.xs
  }
};

// Standard Card Styles
export const CARD_STYLES = {
  base: {
    backgroundColor: COLORS.background.paper,
    borderRadius: BORDER_RADIUS.lg,
    boxShadow: SHADOWS.base,
    padding: SPACING.lg,
    border: `1px solid ${COLORS.grey[200]}`
  },

  header: {
    borderBottom: `1px solid ${COLORS.grey[200]}`,
    paddingBottom: SPACING.md,
    marginBottom: SPACING.lg
  }
};

export default {
  COLORS,
  TYPOGRAPHY,
  SPACING,
  BORDER_RADIUS,
  SHADOWS,
  BUTTON_VARIANTS,
  TABLE_STYLES,
  FORM_STYLES,
  CARD_STYLES,
  BREAKPOINTS,
  RESPONSIVE
};
