import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// material-ui
import { useTheme } from '@mui/material/styles';
import { Box, Card, CardContent, Divider, Grid, Typography, LinearProgress, Chip, Avatar, Stack, Paper } from '@mui/material';

// material-ui icons
import {
  AccessTime as TimeIcon,
  Psychology as PredictionIcon,
  SentimentSatisfied as SatisfiedIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as UnsatisfiedIcon,
  TrendingUp as ConfidenceIcon,
  Star as CommonIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';

// API service
const API_BASE_URL = 'https://laravel-api.fly.dev/api';
import { gridSpacing } from 'store/constant';

export default function AnalyticsMetricsCard({ isLoading: propIsLoading = false, data = null, error: propError = null }) {
  const theme = useTheme();

  // State for API data
  const [metrics, setMetrics] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Fetch statistics from API
  const fetchStatistics = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/session-summaries/statistics`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.data) {
        // Transform API data to match component expectations
        const apiData = result.data;
        const transformedMetrics = {
          session_duration: parseFloat(apiData.average_session_duration) || 0,
          total_predictions: parseInt(apiData.total_predictions) || 0,
          satisfied_count: parseInt(apiData.satisfaction_stats?.total_satisfied) || 0,
          neutral_count: parseInt(apiData.satisfaction_stats?.total_neutral) || 0,
          unsatisfied_count: parseInt(apiData.satisfaction_stats?.total_unsatisfied) || 0,
          average_confidence: parseFloat(apiData.average_confidence) || 0,
          most_common_prediction: apiData.most_common_predictions?.[0]?.most_common_prediction || 'N/A'
        };
        setMetrics(transformedMetrics);
        setLastUpdated(new Date());
      } else {
        throw new Error('Invalid API response format');
      }
    } catch (err) {
      console.error('Error fetching statistics:', err);
      setError(err.message);

      // Fallback to default data if API fails
      setMetrics({
        session_duration: 0,
        total_predictions: 0,
        satisfied_count: 0,
        neutral_count: 0,
        unsatisfied_count: 0,
        average_confidence: 0,
        most_common_prediction: 'N/A'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchStatistics();
  }, []);

  // Refresh function for external use
  const refreshData = () => {
    fetchStatistics();
  };

  // Use provided data or API data
  const finalMetrics = data || metrics;

  // Early return if no data available
  if (!finalMetrics) {
    return <SkeletonTotalGrowthBarChart />;
  }

  // Calculate percentages for sentiment distribution
  const totalSentiments = finalMetrics.satisfied_count + finalMetrics.neutral_count + finalMetrics.unsatisfied_count;
  const satisfiedPercentage = totalSentiments > 0 ? (finalMetrics.satisfied_count / totalSentiments) * 100 : 0;
  const neutralPercentage = totalSentiments > 0 ? (finalMetrics.neutral_count / totalSentiments) * 100 : 0;
  const unsatisfiedPercentage = totalSentiments > 0 ? (finalMetrics.unsatisfied_count / totalSentiments) * 100 : 0;

  // Format session duration
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Format confidence percentage
  const formatConfidence = (confidence) => {
    return `${(confidence * 100).toFixed(1)}%`;
  };

  if (error || propError) {
    return (
      <MainCard>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '300px',
            backgroundColor: 'error.lighter',
            borderRadius: 2,
            border: 2,
            borderColor: 'error.main',
            p: 3
          }}
        >
          <Typography variant="h6" sx={{ color: 'error.main', mb: 2 }}>
            ❌ Erreur de chargement
          </Typography>
          <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center', mb: 2 }}>
            Impossible de charger les données d'analyse: {error || propError}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center' }}>
            Vérifiez votre connexion internet et réessayez.
          </Typography>
        </Box>
      </MainCard>
    );
  }

  if (isLoading || propIsLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  return (
    <MainCard sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
      <Box sx={{ width: '100%', maxWidth: '100%' }}>
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: { xs: 'flex-start', sm: 'center' },
            flexDirection: { xs: 'column', sm: 'row' },
            mb: { xs: 3, sm: 4 },
            gap: { xs: 1, sm: 0 }
          }}
        >
          <AnalyticsIcon
            sx={{
              color: 'primary.main',
              mr: { xs: 0, sm: 2 },
              mb: { xs: 1, sm: 0 },
              fontSize: { xs: '1.75rem', sm: '2rem' }
            }}
          />
          <Box sx={{ textAlign: { xs: 'left', sm: 'left' } }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                mb: 0.5,
                fontSize: { xs: '1.25rem', sm: '1.5rem' }
              }}
            >
              Métriques d'Analyse Prédictive
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
              Analyse des Sentiments
            </Typography>
          </Box>
        </Box>

        {/* Métriques d'Analyse Prédictive Row */}
        <Box sx={{ mb: { xs: 4, sm: 5 } }}>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                color: 'text.primary',
                mb: 1,
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}
            >
              Métriques d'Analyse Prédictive
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
              Indicateurs de performance et prédictions
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: { xs: 2, sm: 3 },
              mx: 0
            }}
          >
            {/* Session Duration */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(25% - 18px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: { xs: '140px', sm: '160px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    borderColor: 'primary.main'
                  }
                }}
              >
                <TimeIcon
                  sx={{
                    color: 'primary.main',
                    fontSize: { xs: '1.75rem', sm: '2rem' },
                    mb: 1.5
                  }}
                />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem' },
                    mb: 1,
                    fontWeight: 500
                  }}
                >
                  Durée Session
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 700,
                    fontSize: { xs: '1.1rem', sm: '1.25rem' },
                    color: 'text.primary'
                  }}
                >
                  {formatDuration(finalMetrics.session_duration)}
                </Typography>
              </Card>
            </Box>

            {/* Total Predictions */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(25% - 18px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: { xs: '140px', sm: '160px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    borderColor: 'secondary.main'
                  }
                }}
              >
                <PredictionIcon
                  sx={{
                    color: 'secondary.main',
                    fontSize: { xs: '1.75rem', sm: '2rem' },
                    mb: 1.5
                  }}
                />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem' },
                    mb: 1,
                    fontWeight: 500
                  }}
                >
                  Prédictions Totales
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 700,
                    fontSize: { xs: '1.1rem', sm: '1.25rem' },
                    color: 'text.primary'
                  }}
                >
                  {finalMetrics.total_predictions.toLocaleString()}
                </Typography>
              </Card>
            </Box>

            {/* Average Confidence */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(25% - 18px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: { xs: '140px', sm: '160px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    borderColor: 'success.main'
                  }
                }}
              >
                <ConfidenceIcon
                  sx={{
                    color: 'success.main',
                    fontSize: { xs: '1.75rem', sm: '2rem' },
                    mb: 1.5
                  }}
                />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem' },
                    mb: 1,
                    fontWeight: 500
                  }}
                >
                  Confiance Moyenne
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 700,
                    fontSize: { xs: '1.1rem', sm: '1.25rem' },
                    color: 'text.primary'
                  }}
                >
                  {formatConfidence(finalMetrics.average_confidence)}
                </Typography>
              </Card>
            </Box>

            {/* Most Common Prediction */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(25% - 18px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: { xs: '140px', sm: '160px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    borderColor: 'warning.main'
                  }
                }}
              >
                <CommonIcon
                  sx={{
                    color: 'warning.main',
                    fontSize: { xs: '1.75rem', sm: '2rem' },
                    mb: 1.5
                  }}
                />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem' },
                    mb: 1,
                    fontWeight: 500
                  }}
                >
                  Prédiction Commune
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 700,
                    fontSize: { xs: '1rem', sm: '1.1rem' },
                    wordBreak: 'break-word',
                    color: 'text.primary'
                  }}
                >
                  {finalMetrics.most_common_prediction}
                </Typography>
              </Card>
            </Box>
          </Box>
        </Box>

        {/* Analyse des Sentiments Row */}
        <Box>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                color: 'text.primary',
                mb: 1,
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}
            >
              Analyse des Sentiments
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
              Distribution et métriques de satisfaction client
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: { xs: 2, sm: 3 },
              mx: 0
            }}
          >
            {/* Satisfied */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(33.333% - 16px)', lg: '1 1 calc(20% - 19.2px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: { xs: '200px', sm: '220px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: '0 6px 24px rgba(0, 0, 0, 0.12)',
                    borderColor: 'success.main'
                  }
                }}
              >
                <Box sx={{ textAlign: 'center', flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Avatar
                    sx={{
                      bgcolor: 'success.main',
                      width: { xs: 40, sm: 48 },
                      height: { xs: 40, sm: 48 },
                      mx: 'auto',
                      mb: 1.5
                    }}
                  >
                    <SatisfiedIcon sx={{ color: 'white', fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
                  </Avatar>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: 'text.primary',
                      fontSize: { xs: '1rem', sm: '1.1rem' },
                      mb: 1
                    }}
                  >
                    Satisfaits
                  </Typography>
                  <Chip
                    label={`${satisfiedPercentage.toFixed(1)}%`}
                    sx={{
                      bgcolor: 'success.main',
                      color: 'white',
                      fontWeight: 600,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      height: { xs: 28, sm: 32 },
                      borderRadius: 2,
                      mb: 1.5,
                      mx: 'auto'
                    }}
                  />
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 800,
                      color: 'success.main',
                      mb: 0.5,
                      fontSize: { xs: '1.5rem', sm: '1.75rem' }
                    }}
                  >
                    {finalMetrics.satisfied_count.toLocaleString()}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      fontWeight: 500,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}
                  >
                    sur {totalSentiments.toLocaleString()}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={satisfiedPercentage}
                  sx={{
                    height: { xs: 4, sm: 6 },
                    borderRadius: 3,
                    backgroundColor: 'success.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'success.main',
                      borderRadius: 3
                    }
                  }}
                />
              </Card>
            </Box>

            {/* Neutral */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(33.333% - 16px)', lg: '1 1 calc(20% - 19.2px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: { xs: '200px', sm: '220px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: '0 6px 24px rgba(0, 0, 0, 0.12)',
                    borderColor: 'warning.main'
                  }
                }}
              >
                <Box sx={{ textAlign: 'center', flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Avatar
                    sx={{
                      bgcolor: 'warning.main',
                      width: { xs: 40, sm: 48 },
                      height: { xs: 40, sm: 48 },
                      mx: 'auto',
                      mb: 1.5
                    }}
                  >
                    <NeutralIcon sx={{ color: 'white', fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
                  </Avatar>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: 'text.primary',
                      fontSize: { xs: '1rem', sm: '1.1rem' },
                      mb: 1
                    }}
                  >
                    Neutres
                  </Typography>
                  <Chip
                    label={`${neutralPercentage.toFixed(1)}%`}
                    sx={{
                      bgcolor: 'warning.main',
                      color: 'white',
                      fontWeight: 600,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      height: { xs: 28, sm: 32 },
                      borderRadius: 2,
                      mb: 1.5,
                      mx: 'auto'
                    }}
                  />
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 800,
                      color: 'warning.main',
                      mb: 0.5,
                      fontSize: { xs: '1.5rem', sm: '1.75rem' }
                    }}
                  >
                    {finalMetrics.neutral_count.toLocaleString()}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      fontWeight: 500,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}
                  >
                    sur {totalSentiments.toLocaleString()}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={neutralPercentage}
                  sx={{
                    height: { xs: 4, sm: 6 },
                    borderRadius: 3,
                    backgroundColor: 'warning.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'warning.main',
                      borderRadius: 3
                    }
                  }}
                />
              </Card>
            </Box>

            {/* Unsatisfied */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(33.333% - 16px)', lg: '1 1 calc(20% - 19.2px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: { xs: '200px', sm: '220px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: '0 6px 24px rgba(0, 0, 0, 0.12)',
                    borderColor: 'error.main'
                  }
                }}
              >
                <Box sx={{ textAlign: 'center', flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Avatar
                    sx={{
                      bgcolor: 'error.main',
                      width: { xs: 40, sm: 48 },
                      height: { xs: 40, sm: 48 },
                      mx: 'auto',
                      mb: 1.5
                    }}
                  >
                    <UnsatisfiedIcon sx={{ color: 'white', fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
                  </Avatar>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: 'text.primary',
                      fontSize: { xs: '1rem', sm: '1.1rem' },
                      mb: 1
                    }}
                  >
                    Insatisfaits
                  </Typography>
                  <Chip
                    label={`${unsatisfiedPercentage.toFixed(1)}%`}
                    sx={{
                      bgcolor: 'error.main',
                      color: 'white',
                      fontWeight: 600,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      height: { xs: 28, sm: 32 },
                      borderRadius: 2,
                      mb: 1.5,
                      mx: 'auto'
                    }}
                  />
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 800,
                      color: 'error.main',
                      mb: 0.5,
                      fontSize: { xs: '1.5rem', sm: '1.75rem' }
                    }}
                  >
                    {finalMetrics.unsatisfied_count.toLocaleString()}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      fontWeight: 500,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}
                  >
                    sur {totalSentiments.toLocaleString()}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={unsatisfiedPercentage}
                  sx={{
                    height: { xs: 4, sm: 6 },
                    borderRadius: 3,
                    backgroundColor: 'error.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'error.main',
                      borderRadius: 3
                    }
                  }}
                />
              </Card>
            </Box>
            {/* Global Satisfaction Summary */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(33.333% - 16px)', lg: '1 1 calc(20% - 19.2px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: { xs: '200px', sm: '220px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: '0 6px 24px rgba(0, 0, 0, 0.12)',
                    borderColor: 'success.main'
                  }
                }}
              >
                <Avatar
                  sx={{
                    bgcolor: 'success.main',
                    width: { xs: 40, sm: 48 },
                    height: { xs: 40, sm: 48 },
                    mx: 'auto',
                    mb: 1.5
                  }}
                >
                  <SatisfiedIcon sx={{ color: 'white', fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
                </Avatar>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 700,
                    color: 'text.primary',
                    fontSize: { xs: '1rem', sm: '1.1rem' },
                    mb: 1.5
                  }}
                >
                  Satisfaction Globale
                </Typography>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 800,
                    color: 'success.main',
                    mb: 0.5,
                    fontSize: { xs: '1.75rem', sm: '2rem' }
                  }}
                >
                  {satisfiedPercentage.toFixed(1)}%
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontWeight: 500,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  {satisfiedPercentage >= 70
                    ? 'Performance excellente'
                    : satisfiedPercentage >= 50
                      ? 'Performance bonne'
                      : satisfiedPercentage >= 30
                        ? 'Performance moyenne'
                        : 'Performance faible'}
                </Typography>
              </Card>
            </Box>

            {/* AI Confidence Summary */}
            <Box
              sx={{
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(33.333% - 16px)', lg: '1 1 calc(20% - 19.2px)' },
                minWidth: 0
              }}
            >
              <Card
                sx={{
                  p: { xs: 2.5, sm: 3 },
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: { xs: '200px', sm: '220px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: '0 6px 24px rgba(0, 0, 0, 0.12)',
                    borderColor: 'primary.main'
                  }
                }}
              >
                <Avatar
                  sx={{
                    bgcolor: 'primary.main',
                    width: { xs: 40, sm: 48 },
                    height: { xs: 40, sm: 48 },
                    mx: 'auto',
                    mb: 1.5
                  }}
                >
                  <ConfidenceIcon sx={{ color: 'white', fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
                </Avatar>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 700,
                    color: 'text.primary',
                    fontSize: { xs: '1rem', sm: '1.1rem' },
                    mb: 1
                  }}
                >
                  Confiance IA
                </Typography>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 800,
                    color: 'primary.main',
                    mb: 1,
                    fontSize: { xs: '1.75rem', sm: '2rem' }
                  }}
                >
                  {formatConfidence(finalMetrics.average_confidence)}
                </Typography>
                <Chip
                  label={finalMetrics.average_confidence >= 0.8 ? 'Élevé' : finalMetrics.average_confidence >= 0.6 ? 'Moyen' : 'Faible'}
                  color={finalMetrics.average_confidence >= 0.8 ? 'success' : finalMetrics.average_confidence >= 0.6 ? 'warning' : 'error'}
                  sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    height: { xs: 28, sm: 32 },
                    fontWeight: 600
                  }}
                />
              </Card>
            </Box>
          </Box>
        </Box>
      </Box>
    </MainCard>
  );
}

AnalyticsMetricsCard.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.object,
  error: PropTypes.string
};
