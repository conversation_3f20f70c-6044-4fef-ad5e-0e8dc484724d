# API des Résumés de Session

Cette API permet de gérer les résumés de session de prédiction.

## Endpoints disponibles

### 1. <PERSON><PERSON><PERSON> un résumé de session
**POST** `/api/session-summaries`

#### <PERSON> requête (JSON)
```json
{
    "session_duration": 15.5,
    "total_predictions": 10,
    "satisfied_count": 7,
    "neutral_count": 2,
    "unsatisfied_count": 1,
    "average_confidence": 0.85,
    "most_common_prediction": "satisfied",
    "session_id": "optional-session-id"
}
```

#### Réponse de succès (201)
```json
{
    "success": true,
    "message": "Résumé de session enregistré avec succès",
    "data": {
        "id": 1,
        "session_duration": 15.5,
        "total_predictions": 10,
        "satisfied_count": 7,
        "neutral_count": 2,
        "unsatisfied_count": 1,
        "average_confidence": 0.85,
        "most_common_prediction": "satisfied",
        "session_id": "optional-session-id",
        "user_agent": "Mozilla/5.0...",
        "ip_address": "***********",
        "created_at": "2025-06-01T10:30:00.000000Z",
        "updated_at": "2025-06-01T10:30:00.000000Z"
    }
}
```

### 2. Récupérer tous les résumés de session
**GET** `/api/session-summaries`

#### Paramètres de requête optionnels
- `per_page`: Nombre d'éléments par page (défaut: 15, max: 100)
- `page`: Numéro de page

#### Réponse de succès (200)
```json
{
    "success": true,
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "session_duration": 15.5,
                "total_predictions": 10,
                // ... autres champs
            }
        ],
        "first_page_url": "https://laravel-api.fly.dev/api/session-summaries?page=1",
        "from": 1,
        "last_page": 1,
        "last_page_url": "https://laravel-api.fly.dev/api/session-summaries?page=1",
        "links": [...],
        "next_page_url": null,
        "path": "https://laravel-api.fly.dev/api/session-summaries",
        "per_page": 15,
        "prev_page_url": null,
        "to": 1,
        "total": 1
    }
}
```

### 3. Récupérer un résumé de session spécifique
**GET** `/api/session-summaries/{id}`

#### Réponse de succès (200)
```json
{
    "success": true,
    "data": {
        "id": 1,
        "session_duration": 15.5,
        "total_predictions": 10,
        "satisfied_count": 7,
        "neutral_count": 2,
        "unsatisfied_count": 1,
        "average_confidence": 0.85,
        "most_common_prediction": "satisfied",
        "session_id": "optional-session-id",
        "user_agent": "Mozilla/5.0...",
        "ip_address": "***********",
        "created_at": "2025-06-01T10:30:00.000000Z",
        "updated_at": "2025-06-01T10:30:00.000000Z"
    }
}
```

### 4. Obtenir des statistiques globales
**GET** `/api/session-summaries/statistics`

#### Réponse de succès (200)
```json
{
    "success": true,
    "data": {
        "total_sessions": 100,
        "average_session_duration": 12.5,
        "total_predictions": 1500,
        "average_confidence": 0.78,
        "satisfaction_stats": {
            "total_satisfied": 1200,
            "total_neutral": 200,
            "total_unsatisfied": 100
        },
        "most_common_predictions": [
            {
                "most_common_prediction": "satisfied",
                "count": 75
            },
            {
                "most_common_prediction": "neutral",
                "count": 20
            },
            {
                "most_common_prediction": "unsatisfied",
                "count": 5
            }
        ]
    }
}
```

## Validation des données

### Champs requis pour la création
- `session_duration`: Nombre décimal >= 0
- `total_predictions`: Entier >= 0
- `satisfied_count`: Entier >= 0
- `neutral_count`: Entier >= 0
- `unsatisfied_count`: Entier >= 0
- `average_confidence`: Nombre décimal entre 0 et 1
- `most_common_prediction`: Chaîne de caractères (max 255)

### Champs optionnels
- `session_id`: Chaîne de caractères (max 255)

### Champs automatiques
- `user_agent`: Récupéré automatiquement depuis les headers
- `ip_address`: Récupérée automatiquement
- `created_at` et `updated_at`: Timestamps automatiques

## Codes de réponse

- **200**: Succès
- **201**: Créé avec succès
- **404**: Ressource non trouvée
- **422**: Erreur de validation
- **500**: Erreur serveur

## Exemples d'utilisation

### Avec cURL
```bash
# Créer un résumé de session
curl -X POST https://laravel-api.fly.dev/api/session-summaries \
  -H "Content-Type: application/json" \
  -d '{
    "session_duration": 15.5,
    "total_predictions": 10,
    "satisfied_count": 7,
    "neutral_count": 2,
    "unsatisfied_count": 1,
    "average_confidence": 0.85,
    "most_common_prediction": "satisfied"
  }'

# Récupérer les statistiques
curl -X GET https://laravel-api.fly.dev/session-summaries/statistics
```

### Avec JavaScript (fetch)
```javascript
// Créer un résumé de session
const response = await fetch('/api/session-summaries', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        session_duration: 15.5,
        total_predictions: 10,
        satisfied_count: 7,
        neutral_count: 2,
        unsatisfied_count: 1,
        average_confidence: 0.85,
        most_common_prediction: 'satisfied'
    })
});

const data = await response.json();
console.log(data);
```
