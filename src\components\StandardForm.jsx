import React from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  Typography,
  useMediaQuery,
  useTheme
} from '@mui/material';

// Design system
import { COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS } from '../themes/designSystem';

/**
 * StandardForm - A responsive form component that follows the design system
 * 
 * Features:
 * - Responsive layout (stacks on mobile, side-by-side on desktop)
 * - Consistent styling using design system
 * - Built-in validation display
 * - Flexible field configuration
 * - Mobile-optimized inputs
 */
const StandardForm = ({
  fields = [],
  values = {},
  errors = {},
  onChange,
  onSubmit,
  children,
  title,
  subtitle,
  className = '',
  sx = {},
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleFieldChange = (fieldName, value) => {
    if (onChange) {
      onChange(fieldName, value);
    }
  };

  const renderField = (field) => {
    const {
      name,
      label,
      type = 'text',
      required = false,
      options = [],
      placeholder,
      multiline = false,
      rows = 4,
      disabled = false,
      helperText,
      fullWidth = true,
      size = 'medium',
      ...fieldProps
    } = field;

    const fieldValue = values[name] || '';
    const fieldError = errors[name];
    const hasError = Boolean(fieldError);

    const commonProps = {
      fullWidth,
      required,
      disabled,
      error: hasError,
      helperText: fieldError || helperText,
      size: isMobile ? 'small' : size,
      sx: {
        '& .MuiOutlinedInput-root': {
          borderRadius: BORDER_RADIUS.md,
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: COLORS.primary.light
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: COLORS.primary.main,
            borderWidth: '2px'
          }
        },
        '& .MuiInputLabel-root': {
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          fontSize: isMobile ? TYPOGRAPHY.fontSize.sm : TYPOGRAPHY.fontSize.md,
          '&.Mui-focused': {
            color: COLORS.primary.main
          }
        },
        '& .MuiFormHelperText-root': {
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          fontSize: TYPOGRAPHY.fontSize.xs
        }
      }
    };

    switch (type) {
      case 'select':
        return (
          <FormControl {...commonProps} key={name}>
            <InputLabel>{label}</InputLabel>
            <Select
              value={fieldValue}
              onChange={(e) => handleFieldChange(name, e.target.value)}
              label={label}
              {...fieldProps}
            >
              {options.map((option) => (
                <MenuItem 
                  key={option.value} 
                  value={option.value}
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: isMobile ? TYPOGRAPHY.fontSize.sm : TYPOGRAPHY.fontSize.md
                  }}
                >
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {(fieldError || helperText) && (
              <FormHelperText>{fieldError || helperText}</FormHelperText>
            )}
          </FormControl>
        );

      case 'textarea':
        return (
          <TextField
            {...commonProps}
            key={name}
            name={name}
            label={label}
            value={fieldValue}
            onChange={(e) => handleFieldChange(name, e.target.value)}
            placeholder={placeholder}
            multiline
            rows={isMobile ? Math.max(2, rows - 1) : rows}
            {...fieldProps}
          />
        );

      default:
        return (
          <TextField
            {...commonProps}
            key={name}
            name={name}
            label={label}
            type={type}
            value={fieldValue}
            onChange={(e) => handleFieldChange(name, e.target.value)}
            placeholder={placeholder}
            multiline={multiline}
            rows={multiline ? (isMobile ? Math.max(2, rows - 1) : rows) : undefined}
            {...fieldProps}
          />
        );
    }
  };

  return (
    <Box
      component="form"
      onSubmit={onSubmit}
      className={`responsive-form ${className}`}
      sx={{
        width: '100%',
        ...sx
      }}
      {...props}
    >
      {/* Form Header */}
      {(title || subtitle) && (
        <Box sx={{ mb: { xs: 2, md: 3 } }}>
          {title && (
            <Typography
              variant="h5"
              component="h2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.bold,
                color: COLORS.text.primary,
                fontSize: { xs: TYPOGRAPHY.fontSize.lg, md: TYPOGRAPHY.fontSize.xl },
                mb: subtitle ? 1 : 0
              }}
            >
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography
              variant="body1"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                fontSize: { xs: TYPOGRAPHY.fontSize.sm, md: TYPOGRAPHY.fontSize.md }
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      )}

      {/* Form Fields */}
      <Grid container spacing={{ xs: 2, md: 3 }}>
        {fields.map((field) => {
          const gridSize = field.gridSize || { xs: 12, md: 6 };
          
          return (
            <Grid item {...gridSize} key={field.name}>
              {renderField(field)}
            </Grid>
          );
        })}
      </Grid>

      {/* Custom Content */}
      {children && (
        <Box sx={{ mt: { xs: 2, md: 3 } }}>
          {children}
        </Box>
      )}
    </Box>
  );
};

StandardForm.propTypes = {
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      type: PropTypes.oneOf(['text', 'email', 'password', 'number', 'tel', 'url', 'select', 'textarea']),
      required: PropTypes.bool,
      options: PropTypes.arrayOf(
        PropTypes.shape({
          value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
          label: PropTypes.string.isRequired
        })
      ),
      placeholder: PropTypes.string,
      multiline: PropTypes.bool,
      rows: PropTypes.number,
      disabled: PropTypes.bool,
      helperText: PropTypes.string,
      fullWidth: PropTypes.bool,
      size: PropTypes.oneOf(['small', 'medium']),
      gridSize: PropTypes.object
    })
  ).isRequired,
  values: PropTypes.object,
  errors: PropTypes.object,
  onChange: PropTypes.func,
  onSubmit: PropTypes.func,
  children: PropTypes.node,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  className: PropTypes.string,
  sx: PropTypes.object
};

export default StandardForm;
