import React, { useEffect, useState } from 'react';
import {
  fetchAllSousSousCategories,
  createSousSousCategorie,
  updateSousSousCategorie,
  deleteSousSousCategorie,
  fetchAllSousCategories,
  fetchModelImages
} from '../../services/categoryService';
import { Form, Spinner, Container, Row, Col, Toast, ToastContainer, Badge, Breadcrumb, Button } from 'react-bootstrap';
import { Box, Typography, Alert } from '@mui/material';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaHome, FaLayerGroup } from 'react-icons/fa';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';
import { FormModal, ConfirmationModal } from '../../ui-component/extended/ModalVariants';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';
import MainCard from '../../ui-component/cards/MainCard';
import StandardButton from '../../components/StandardButton';
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';

const initialForm = { nom: '', description: '', sous_categorie_id: '' };

export default function SousSousCategories({ ImageManager }) {
  // Columns for sous-sous-categories table
  const sousSousCategoriesColumns = [
    { id: 'id', label: 'ID', minWidth: 60 },
    { id: 'nom', label: 'Nom', minWidth: 150 },
    { id: 'description', label: 'Description', minWidth: 200 },
    { id: 'image', label: 'Image', minWidth: 120 },
    { id: 'parent', label: 'Sous-catégorie parente', minWidth: 150 },
    { id: 'actions', label: 'Actions', minWidth: 120, align: 'center' }
  ];

  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [form, setForm] = useState(initialForm);
  const [imageFile, setImageFile] = useState(null);
  const [editingId, setEditingId] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [toast, setToast] = useState({ show: false, message: '', variant: 'success' });

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'

  // Delete confirmation modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const loadSousSousCategories = async () => {
    console.log('🔄 Loading sous-sous-categories...');
    setLoading(true);
    setError('');
    try {
      const data = await fetchAllSousSousCategories();
      console.log('✅ Sous-sous-categories loaded:', data);

      // Fetch images for each sous sous category
      const categoriesWithImages = await Promise.all(
        data.map(async (ssc) => {
          try {
            const images = await fetchModelImages('sous_sous_categorie', ssc.id);
            const primaryImage = images.find((img) => img.is_primary) || images[0];
            return {
              ...ssc,
              primary_image_url: primaryImage ? `/api/images/serve/${primaryImage.id}` : null,
              images: images
            };
          } catch (error) {
            console.warn(`Failed to fetch images for sous sous category ${ssc.id}:`, error);
            return { ...ssc, primary_image_url: null, images: [] };
          }
        })
      );

      console.log('✅ Sous-sous-categories with images:', categoriesWithImages);
      setSousSousCategories(categoriesWithImages);
    } catch (e) {
      console.error('❌ Error loading sous-sous-categories:', e);
      setError(e.message);
    }
    setLoading(false);
  };

  const loadSousCategories = async () => {
    try {
      const data = await fetchAllSousCategories();
      setSousCategories(data);
    } catch (e) {
      // Optionally show error
    }
  };

  useEffect(() => {
    loadSousSousCategories();
    loadSousCategories();
  }, []);

  const handleChange = (e) => {
    const newForm = { ...form, [e.target.name]: e.target.value };
    console.log(`📝 Sous-sous-category form field changed: ${e.target.name} = ${e.target.value}`);
    console.log('📋 Updated sous-sous-category form:', newForm);
    setForm(newForm);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      console.log(`📝 ${editingId ? 'Updating' : 'Creating'} sous-sous-category:`, form);

      // Prepare data with image file
      const formDataWithImage = {
        ...form,
        imageFile: imageFile
      };

      if (editingId) {
        console.log(`🔄 Updating sous-sous-category with ID: ${editingId}`);
        await updateSousSousCategorie(editingId, formDataWithImage);
        console.log('✅ Sous-sous-category updated successfully');
        setToast({ show: true, message: 'Sous-sous-catégorie mise à jour !', variant: 'success' });
      } else {
        console.log('➕ Creating new sous-sous-category');
        await createSousSousCategorie(formDataWithImage);
        console.log('✅ Sous-sous-category created successfully');
        setToast({ show: true, message: 'Sous-sous-catégorie ajoutée !', variant: 'success' });
      }

      setForm(initialForm);
      setImageFile(null);
      setEditingId(null);
      setShowModal(false);
      loadSousSousCategories();
    } catch (e) {
      console.error('❌ Error submitting sous-sous-category:', e);
      setError(e.message);
      setToast({ show: true, message: e.message, variant: 'danger' });
    }
    setSubmitting(false);
  };

  // Open modal for creating
  const handleCreate = () => {
    setModalAction('create');
    setEditingId(null);
    setForm(initialForm);
    setImageFile(null);
    setShowModal(true);
  };

  // Open modal for editing
  const handleEdit = (ssc) => {
    console.log('✏️ Opening sous-sous-category edit modal for:', ssc);
    setModalAction('edit');
    const formData = {
      nom: ssc.nom_sous_sous_categorie || ssc.nom || '',
      description: ssc.description_sous_sous_categorie || ssc.description || '',
      sous_categorie_id: ssc.sous_categorie_id || ''
    };
    console.log('📝 Setting sous-sous-category form data:', formData);
    setForm(formData);
    setEditingId(ssc.id);
    setShowModal(true);
  };

  // Close modal
  const handleModalClose = () => {
    setShowModal(false);
    setEditingId(null);
    setForm(initialForm);
    setImageFile(null);
    setError('');
  };

  // Confirm delete
  const confirmDelete = (id, name) => {
    setItemToDelete({ id, name });
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!itemToDelete) return;

    setDeleteLoading(true);
    setError('');

    try {
      const { id } = itemToDelete;
      console.log(`🗑️ Starting delete process for sous-sous-category with ID: ${id}`);

      await deleteSousSousCategorie(id);
      console.log('✅ Sous-sous-category deleted, reloading data...');

      setShowDeleteModal(false);
      setItemToDelete(null);
      setToast({ show: true, message: 'Sous-sous-catégorie supprimée !', variant: 'success' });
      loadSousSousCategories();
    } catch (e) {
      console.error('❌ Delete operation failed:', e);
      setError(e.message);
      setToast({ show: true, message: e.message, variant: 'danger' });
    }

    setDeleteLoading(false);
  };

  // Close delete modal
  const handleDeleteModalClose = () => {
    if (!deleteLoading) {
      setShowDeleteModal(false);
      setItemToDelete(null);
    }
  };

  // Render cell content based on column
  const renderCell = (column, row, value) => {
    switch (column.id) {
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, fontWeight: TYPOGRAPHY.fontWeight.medium }}>
            {row.id}
          </Typography>
        );
      case 'nom':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, fontWeight: TYPOGRAPHY.fontWeight.semibold }}>
            {row.nom || row.nom_sous_sous_categorie}
          </Typography>
        );
      case 'description':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, color: COLORS.text.secondary }}>
            {row.description || row.description_sous_sous_categorie || 'Aucune description'}
          </Typography>
        );
      case 'image':
        return row.primary_image_url ? (
          <img
            src={row.primary_image_url}
            alt=""
            style={{ width: 40, height: 40, borderRadius: 4, objectFit: 'cover' }}
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/40';
            }}
          />
        ) : (
          <Typography variant="body2" sx={{ color: COLORS.text.secondary, fontStyle: 'italic' }}>
            Aucune image
          </Typography>
        );
      case 'parent':
        const parent = sousCategories.find((sc) => sc.id === parseInt(row.sous_categorie_id));
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
            {parent ? parent.nom || parent.nom_sous_categorie : `ID: ${row.sous_categorie_id}`}
          </Typography>
        );
      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton variant="outlined" size="small" onClick={() => handleEdit(row)} sx={{ minWidth: 'auto', padding: '4px 8px' }}>
              <FaPencilAlt />
            </StandardButton>
            <StandardButton
              variant="outlined"
              color="error"
              size="small"
              onClick={() => confirmDelete(row.id, row.nom || row.nom_sous_sous_categorie)}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );
      default:
        return value;
    }
  };

  return (
    <MainCard title="Gestion des Sous-Sous-Catégories">
      <Box sx={{ width: '100%' }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="h4"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontSize: TYPOGRAPHY.fontSize.xl,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            <FaLayerGroup style={{ marginRight: 8 }} />
            Gestion des Sous-Sous-Catégories
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary
            }}
          >
            Gérez les sous-sous-catégories de votre catalogue produits
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Actions */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography
            variant="h6"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.text.dark
            }}
          >
            Liste des sous-sous-catégories ({sousSousCategories.length})
          </Typography>
          <StandardButton variant="contained" startIcon={<FaPlus />} onClick={handleCreate}>
            Ajouter une sous-sous-catégorie
          </StandardButton>
        </Box>

        {/* Table */}
        <StandardCard>
          <StandardTable
            columns={sousSousCategoriesColumns}
            data={sousSousCategories}
            loading={loading}
            renderCell={renderCell}
            emptyMessage="Aucune sous-sous-catégorie trouvée"
            loadingMessage="Chargement des sous-sous-catégories..."
          />
        </StandardCard>

        {/* Toast Container */}
        <ToastContainer position="bottom-end" className="p-3">
          <Toast bg={toast.variant} show={toast.show} onClose={() => setToast({ ...toast, show: false })} delay={3000} autohide>
            <Toast.Body className="text-white">{toast.message}</Toast.Body>
          </Toast>
        </ToastContainer>
      </Box>

      {/* Professional Edit/Create Modal */}
      <FormModal
        show={showModal}
        onHide={handleModalClose}
        onSubmit={handleSubmit}
        title={modalAction === 'edit' ? 'Modifier une sous-sous-catégorie' : 'Ajouter une nouvelle sous-sous-catégorie'}
        isEdit={modalAction === 'edit'}
        loading={submitting}
        size="lg"
        submitText={modalAction === 'edit' ? 'Mettre à jour' : 'Ajouter'}
        cancelText="Annuler"
        icon={modalAction === 'edit' ? <FaPencilAlt /> : <FaPlus />}
      >
        <Form id="sousSousCatForm" onSubmit={handleSubmit}>
          <Row className="g-3">
            <Col xs={12} md={6}>
              <Form.Group controlId="sousSousCategorieNom">
                <Form.Label className="fw-medium">Nom de la sous-sous-catégorie</Form.Label>
                <Form.Control
                  name="nom"
                  value={form.nom || ''}
                  onChange={handleChange}
                  placeholder="Nom de la sous-sous-catégorie"
                  required
                  disabled={submitting}
                  className="rounded-3 border-2"
                />
                <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
              </Form.Group>
            </Col>
            <Col xs={12} md={6}>
              <Form.Group controlId="sousSousCategorieImage">
                <Form.Label className="fw-medium">Image</Form.Label>
                <Form.Control
                  type="file"
                  accept="image/*"
                  onChange={(e) => setImageFile(e.target.files[0])}
                  disabled={submitting}
                  className="rounded-3 border-2"
                />
                <Form.Text className="text-muted">
                  Sélectionnez une image pour la sous-sous-catégorie (max 10MB).
                  {imageFile && <span className="text-success ms-2">✓ {imageFile.name}</span>}
                </Form.Text>
              </Form.Group>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12} md={6}>
              <Form.Group controlId="sousCategorieId">
                <Form.Label className="fw-medium">Sous-catégorie parente</Form.Label>
                <Form.Select
                  name="sous_categorie_id"
                  value={form.sous_categorie_id || ''}
                  onChange={handleChange}
                  required
                  disabled={submitting || sousCategories.length === 0}
                  className="rounded-3 border-2"
                >
                  <option value="">Sélectionnez une sous-catégorie</option>
                  {sousCategories.map((sc) => (
                    <option key={sc.id} value={sc.id}>
                      {sc.nom || sc.nom_sous_categorie || `ID ${sc.id}`}
                    </option>
                  ))}
                </Form.Select>
                <Form.Text className="text-muted">
                  Sélectionnez la sous-catégorie à laquelle cette sous-sous-catégorie appartient.
                </Form.Text>
              </Form.Group>
            </Col>
            <Col xs={12} md={6}>
              {/* Empty column for layout balance */}
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              <Form.Group controlId="sousSousCategorieDescription">
                <Form.Label className="fw-medium">Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={form.description || ''}
                  onChange={handleChange}
                  placeholder="Description détaillée de la sous-sous-catégorie"
                  disabled={submitting}
                  className="rounded-3 border-2"
                />
              </Form.Group>
            </Col>
          </Row>
        </Form>
      </FormModal>

      {/* Professional Delete Confirmation Modal */}
      <ConfirmationModal
        show={showDeleteModal}
        onHide={handleDeleteModalClose}
        onConfirm={handleDelete}
        title="Confirmer la suppression"
        message={
          itemToDelete && (
            <>
              Êtes-vous sûr de vouloir supprimer la sous-sous-catégorie <strong>"{itemToDelete.name}"</strong> ?<br />
              <small className="text-muted">
                Cette action supprimera également tous les produits associés à cette sous-sous-catégorie.
              </small>
            </>
          )
        }
        confirmText="Supprimer définitivement"
        cancelText="Annuler"
        variant="danger"
        loading={deleteLoading}
        icon={<FaTrashAlt />}
      />
    </MainCard>
  );
}
