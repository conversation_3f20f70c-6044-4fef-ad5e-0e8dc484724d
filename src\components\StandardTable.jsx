import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  CircularProgress,
  Alert,
  Typography,
  Pagination,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { COLORS, TYPOGRAPHY, SPACING, TABLE_STYLES, BREAKPOINTS } from '../themes/designSystem';

/**
 * Standardized Table Component
 * Based on the client page table styling for consistency across all pages
 *
 * @param {Object} props
 * @param {Array} props.columns - Array of column definitions: [{ id: 'name', label: 'Name', minWidth: 170 }]
 * @param {Array} props.data - Array of data objects
 * @param {boolean} props.loading - Loading state
 * @param {string} props.error - Error message
 * @param {string} props.emptyMessage - Message when no data
 * @param {Function} props.renderCell - Custom cell renderer: (column, row, value) => JSX
 * @param {boolean} props.hover - Enable row hover effect
 * @param {boolean} props.striped - Enable striped rows
 * @param {Object} props.pagination - Pagination config: { page, totalPages, onPageChange }
 * @param {Object} props.sx - Additional styling
 */
const StandardTable = ({
  columns = [],
  data = [],
  loading = false,
  error = null,
  emptyMessage = 'Aucune donnée disponible',
  renderCell = null,
  hover = true,
  striped = false,
  pagination = null,
  sx = {},
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'lg'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  // Loading state
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <Typography
        variant="body1"
        sx={{
          p: 3,
          textAlign: 'center',
          color: COLORS.text.secondary,
          fontStyle: 'italic'
        }}
      >
        {emptyMessage}
      </Typography>
    );
  }

  // Default cell renderer
  const defaultRenderCell = (column, row, value) => {
    if (value === null || value === undefined) {
      return <span style={{ color: COLORS.text.secondary, fontStyle: 'italic' }}>N/A</span>;
    }
    return value;
  };

  const cellRenderer = renderCell || defaultRenderCell;

  // Get responsive styles based on screen size
  const getResponsiveTableStyles = () => {
    if (isMobile) {
      return {
        container: {
          ...TABLE_STYLES.responsive.mobile.container,
          overflowX: 'auto',
          WebkitOverflowScrolling: 'touch',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          border: `1px solid ${COLORS.grey[200]}`,
          borderRadius: '8px',
          background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
          margin: `0 -${SPACING.sm}`,
          padding: `0 ${SPACING.sm}`
        },
        table: {
          ...TABLE_STYLES.responsive.mobile.table,
          minWidth: '600px'
        },
        headerCell: {
          ...TABLE_STYLES.responsive.mobile.header,
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          fontWeight: TYPOGRAPHY.fontWeight.bold,
          color: COLORS.text.dark,
          textTransform: 'uppercase',
          letterSpacing: '0.05em',
          position: 'sticky',
          top: 0,
          zIndex: 10,
          backgroundColor: COLORS.background.paper
        },
        bodyCell: {
          ...TABLE_STYLES.responsive.mobile.cell,
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          color: COLORS.text.primary
        }
      };
    } else if (isTablet) {
      return {
        container: {
          ...TABLE_STYLES.responsive.tablet.container,
          overflowX: 'auto',
          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
          border: `1px solid ${COLORS.grey[200]}`,
          borderRadius: '12px',
          background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)'
        },
        table: {
          ...TABLE_STYLES.responsive.tablet.table,
          minWidth: '700px'
        },
        headerCell: {
          ...TABLE_STYLES.responsive.tablet.header,
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          fontWeight: TYPOGRAPHY.fontWeight.bold,
          color: COLORS.text.dark,
          textTransform: 'uppercase',
          letterSpacing: '0.05em'
        },
        bodyCell: {
          ...TABLE_STYLES.responsive.tablet.cell,
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          color: COLORS.text.primary
        }
      };
    } else {
      return {
        container: {
          overflowX: 'visible',
          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
          border: `1px solid ${COLORS.grey[200]}`,
          borderRadius: '16px',
          background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
          overflow: 'hidden'
        },
        table: {
          minWidth: 'auto'
        },
        headerCell: {
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          fontSize: TYPOGRAPHY.fontSize.sm,
          fontWeight: TYPOGRAPHY.fontWeight.bold,
          color: COLORS.text.dark,
          textTransform: 'uppercase',
          letterSpacing: '0.05em',
          padding: `${SPACING.lg} ${SPACING.md}`,
          borderBottom: `2px solid ${COLORS.grey[200]}`,
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '2px',
            background: `linear-gradient(90deg, ${COLORS.primary.main}, ${COLORS.secondary.main})`,
            opacity: 0.3
          }
        },
        bodyCell: {
          ...TABLE_STYLES.cell,
          fontFamily: TYPOGRAPHY.fontFamily.primary,
          fontSize: TYPOGRAPHY.fontSize.sm,
          color: COLORS.text.primary
        }
      };
    }
  };

  const responsiveStyles = getResponsiveTableStyles();

  return (
    <Box sx={{ width: '100%', ...sx }}>
      <TableContainer component={Paper} sx={responsiveStyles.container} className="responsive-table-container">
        <Table sx={responsiveStyles.table} {...props}>
          <TableHead>
            <TableRow
              sx={{
                background: `linear-gradient(135deg, ${COLORS.grey[50]} 0%, ${COLORS.grey[100]} 100%)`,
                '& th': {
                  borderBottom: `2px solid ${COLORS.primary.main}20`
                }
              }}
            >
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  sx={{
                    ...responsiveStyles.headerCell,
                    minWidth: isMobile ? 'auto' : column.minWidth,
                    maxWidth: isMobile ? '150px' : 'none',
                    overflow: isMobile ? 'hidden' : 'visible',
                    textOverflow: isMobile ? 'ellipsis' : 'clip',
                    whiteSpace: isMobile ? 'nowrap' : 'normal'
                  }}
                >
                  {column.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((row, index) => (
              <TableRow
                key={row.id || index}
                sx={{
                  backgroundColor: striped && index % 2 === 1 ? COLORS.grey[50] : 'transparent',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  position: 'relative',
                  '&:hover': hover
                    ? {
                        backgroundColor: `${COLORS.primary[50]}`,
                        transform: 'translateY(-1px)',
                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)',
                        '& td': {
                          borderColor: COLORS.primary[200]
                        }
                      }
                    : {},
                  cursor: hover ? 'pointer' : 'default',
                  '&:last-child td': {
                    borderBottom: 'none'
                  }
                }}
              >
                {columns.map((column) => {
                  const value = row[column.id];
                  return (
                    <TableCell
                      key={column.id}
                      sx={{
                        ...responsiveStyles.bodyCell,
                        maxWidth: isMobile ? '150px' : 'none',
                        overflow: isMobile ? 'hidden' : 'visible',
                        textOverflow: isMobile ? 'ellipsis' : 'clip',
                        whiteSpace: isMobile ? 'nowrap' : 'normal'
                      }}
                    >
                      {cellRenderer(column, row, value)}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Responsive Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            mt: isMobile ? 1 : 2,
            px: isMobile ? SPACING.sm : 0
          }}
        >
          <Pagination
            count={pagination.totalPages}
            page={pagination.page}
            onChange={(event, page) => pagination.onPageChange(page)}
            color="primary"
            showFirstButton={!isMobile}
            showLastButton={!isMobile}
            siblingCount={isMobile ? 0 : 1}
            boundaryCount={isMobile ? 1 : 2}
            size={isMobile ? 'small' : 'medium'}
            sx={{
              '& .MuiPaginationItem-root': {
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontSize: isMobile ? TYPOGRAPHY.fontSize.xs : TYPOGRAPHY.fontSize.sm,
                minWidth: isMobile ? '32px' : '40px',
                height: isMobile ? '32px' : '40px'
              }
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default StandardTable;
