import React, { useState } from 'react';
import { Box, Button, Typography, CircularProgress, Alert, Chip, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';
import {
  BugReport as BugReportIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { runApiDiagnostic, getRecommendations } from '../utils/apiDiagnostic';
import ProfessionalModal from '../ui-component/extended/ProfessionalModal';

const QuickDiagnostic = ({ open, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);

  const runQuickDiagnostic = async () => {
    setLoading(true);
    try {
      const diagnosticResults = await runApiDiagnostic();
      setResults(diagnosticResults);
    } catch (error) {
      console.error('Erreur lors du diagnostic:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'PASS':
        return <CheckCircleIcon sx={{ color: 'success.main' }} />;
      case 'FAIL':
        return <ErrorIcon sx={{ color: 'error.main' }} />;
      default:
        return <WarningIcon sx={{ color: 'warning.main' }} />;
    }
  };

  const handleClose = () => {
    setResults(null);
    onClose();
  };

  return (
    <ProfessionalModal
      show={open}
      onHide={handleClose}
      title="Diagnostic Rapide API"
      icon={<BugReportIcon />}
      variant="info"
      size="lg"
      showFooter={true}
      primaryButton={results ? true : false}
      secondaryButton={true}
      primaryAction={results ? runQuickDiagnostic : undefined}
      secondaryAction={handleClose}
      primaryText={results ? 'Relancer' : undefined}
      secondaryText="Fermer"
      disabled={loading}
    >
      {!results && !loading && (
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body1" gutterBottom>
            Lancez un diagnostic pour identifier les problèmes de connexion avec l'API.
          </Typography>
          <Button variant="contained" onClick={runQuickDiagnostic} startIcon={<BugReportIcon />} sx={{ mt: 2 }}>
            Lancer le diagnostic
          </Button>
        </Box>
      )}

      {loading && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 2 }}>
            Diagnostic en cours...
          </Typography>
        </Box>
      )}

      {results && (
        <Box>
          {/* Résumé */}
          <Alert
            severity={
              results.tests.some((t) => t.status === 'FAIL')
                ? 'error'
                : results.tests.some((t) => t.status === 'SKIP')
                  ? 'warning'
                  : 'success'
            }
            sx={{ mb: 2 }}
          >
            <Typography variant="h6" gutterBottom>
              Résumé
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip label={`✅ ${results.tests.filter((t) => t.status === 'PASS').length} réussis`} color="success" size="small" />
              <Chip label={`❌ ${results.tests.filter((t) => t.status === 'FAIL').length} échoués`} color="error" size="small" />
              <Chip label={`⏭️ ${results.tests.filter((t) => t.status === 'SKIP').length} ignorés`} color="warning" size="small" />
            </Box>
          </Alert>

          {/* Recommandations */}
          {getRecommendations(results).length > 0 && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                🎯 Recommandations
              </Typography>
              <List dense>
                {getRecommendations(results).map((rec, index) => (
                  <ListItem key={index} sx={{ py: 0 }}>
                    <ListItemText primary={rec} />
                  </ListItem>
                ))}
              </List>
            </Alert>
          )}

          {/* Tests détaillés */}
          <Typography variant="h6" gutterBottom>
            Tests effectués
          </Typography>
          <List>
            {results.tests.map((test, index) => (
              <ListItem key={index}>
                <ListItemIcon>{getStatusIcon(test.status)}</ListItemIcon>
                <ListItemText
                  primary={test.name}
                  secondary={
                    test.error ? (
                      <Typography variant="body2" color="error">
                        {test.error}
                      </Typography>
                    ) : test.details?.status ? (
                      <Typography variant="body2" color="text.secondary">
                        Status: {test.details.status}
                      </Typography>
                    ) : null
                  }
                />
                <Chip
                  label={test.status}
                  color={test.status === 'PASS' ? 'success' : test.status === 'FAIL' ? 'error' : 'warning'}
                  size="small"
                />
              </ListItem>
            ))}
          </List>

          {/* Informations environnement */}
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Environnement
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Chip label={`API: ${results.environment.apiUrl}`} size="small" />
            <Chip
              label={results.environment.hasToken ? 'Token: ✅' : 'Token: ❌'}
              size="small"
              color={results.environment.hasToken ? 'success' : 'error'}
            />
            <Chip
              label={results.environment.online ? 'En ligne: ✅' : 'Hors ligne: ❌'}
              size="small"
              color={results.environment.online ? 'success' : 'error'}
            />
          </Box>
        </Box>
      )}
    </ProfessionalModal>
  );
};

export default QuickDiagnostic;
