// ==============================|| RESPONSIVE UTILITIES ||============================== //

// Import design system variables and map module
@use 'sass:map';
@use 'themes-vars.module.scss' as *;

// ==============================|| BREAKPOINTS ||============================== //

$breakpoints: (
  xs: 0,
  sm: 600px,
  md: 900px,
  lg: 1200px,
  xl: 1536px
);

// ==============================|| RESPONSIVE MIXINS ||============================== //

@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $value: map.get($breakpoints, $breakpoint);
    @if $value == 0 {
      @content;
    } @else {
      @media (min-width: $value) {
        @content;
      }
    }
  }
}

@mixin respond-below($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $value: map.get($breakpoints, $breakpoint);
    @if $value > 0 {
      @media (max-width: $value - 1px) {
        @content;
      }
    }
  }
}

// ==============================|| RESPONSIVE CONTAINERS ||============================== //

.responsive-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;

  @include respond-to(sm) {
    max-width: 540px;
    padding: 0 1.5rem;
  }

  @include respond-to(md) {
    max-width: 720px;
    padding: 0 2rem;
  }

  @include respond-to(lg) {
    max-width: 960px;
  }

  @include respond-to(xl) {
    max-width: 1140px;
  }
}

// ==============================|| RESPONSIVE GRID ||============================== //

.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;

  @include respond-to(sm) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @include respond-to(md) {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  @include respond-to(lg) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.responsive-grid-2 {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;

  @include respond-to(md) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

// ==============================|| RESPONSIVE FLEXBOX ||============================== //

.responsive-flex {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  @include respond-to(md) {
    flex-direction: row;
    gap: 2rem;
  }
}

.responsive-flex-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;

  > * {
    flex: 1 1 100%;

    @include respond-to(sm) {
      flex: 1 1 calc(50% - 0.5rem);
    }

    @include respond-to(md) {
      flex: 1 1 calc(33.333% - 0.667rem);
    }

    @include respond-to(lg) {
      flex: 1 1 calc(25% - 0.75rem);
    }
  }
}

// ==============================|| RESPONSIVE SPACING ||============================== //

// Responsive padding classes
.responsive-padding {
  padding: 0.5rem;

  @include respond-to(sm) {
    padding: 1rem;
  }

  @include respond-to(md) {
    padding: 1.5rem;
  }

  @include respond-to(lg) {
    padding: 2rem;
  }
}

.responsive-padding-x {
  padding-left: 0.5rem;
  padding-right: 0.5rem;

  @include respond-to(sm) {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @include respond-to(md) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @include respond-to(lg) {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.responsive-padding-y {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;

  @include respond-to(sm) {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  @include respond-to(md) {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  @include respond-to(lg) {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

// Responsive margin classes
.responsive-margin {
  margin: 0.5rem;

  @include respond-to(sm) {
    margin: 1rem;
  }

  @include respond-to(md) {
    margin: 1.5rem;
  }

  @include respond-to(lg) {
    margin: 2rem;
  }
}

.responsive-margin-bottom {
  margin-bottom: 0.5rem;

  @include respond-to(sm) {
    margin-bottom: 1rem;
  }

  @include respond-to(md) {
    margin-bottom: 1.5rem;
  }

  @include respond-to(lg) {
    margin-bottom: 2rem;
  }
}

// ==============================|| RESPONSIVE TYPOGRAPHY ||============================== //

.responsive-text {
  font-size: 0.875rem;
  line-height: 1.4;

  @include respond-to(sm) {
    font-size: 1rem;
    line-height: 1.5;
  }

  @include respond-to(md) {
    font-size: 1.125rem;
    line-height: 1.6;
  }
}

.responsive-heading {
  font-size: 1.25rem;
  line-height: 1.3;
  font-weight: 600;

  @include respond-to(sm) {
    font-size: 1.5rem;
    line-height: 1.4;
  }

  @include respond-to(md) {
    font-size: 1.875rem;
    line-height: 1.5;
  }

  @include respond-to(lg) {
    font-size: 2.25rem;
  }
}

.responsive-subheading {
  font-size: 1rem;
  line-height: 1.4;
  font-weight: 500;

  @include respond-to(sm) {
    font-size: 1.125rem;
    line-height: 1.5;
  }

  @include respond-to(md) {
    font-size: 1.25rem;
  }
}

// ==============================|| RESPONSIVE TABLES ||============================== //

.responsive-table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

  @include respond-below(md) {
    margin: 0 -1rem;
    border-radius: 0;
  }

  table {
    width: 100%;
    min-width: 600px;

    @include respond-to(md) {
      min-width: auto;
    }

    th, td {
      padding: 0.5rem;
      font-size: 0.75rem;
      white-space: nowrap;

      @include respond-to(sm) {
        padding: 0.75rem;
        font-size: 0.875rem;
      }

      @include respond-to(md) {
        padding: 1rem;
        font-size: 1rem;
        white-space: normal;
      }
    }

    th {
      background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      position: sticky;
      top: 0;
      z-index: 10;

      @include respond-below(md) {
        font-size: 0.625rem;
      }
    }
  }
}

// ==============================|| RESPONSIVE CARDS ||============================== //

.responsive-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 1rem;

  @include respond-to(sm) {
    padding: 1.5rem;
    border-radius: 0.75rem;
  }

  @include respond-to(md) {
    padding: 2rem;
    border-radius: 1rem;
  }
}

.responsive-card-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;

  @include respond-to(sm) {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  @include respond-to(lg) {
    gap: 2rem;
  }
}

// ==============================|| RESPONSIVE FORMS ||============================== //

.responsive-form {
  .form-group {
    margin-bottom: 1rem;

    @include respond-to(md) {
      margin-bottom: 1.5rem;
    }
  }

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    @include respond-to(md) {
      flex-direction: row;
      gap: 1.5rem;
    }

    .form-group {
      flex: 1;
      margin-bottom: 0;
    }
  }

  .form-control {
    width: 100%;
    padding: 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;

    @include respond-to(sm) {
      padding: 0.875rem;
      font-size: 1rem;
    }

    @include respond-to(md) {
      padding: 1rem;
    }
  }

  .btn {
    width: 100%;
    padding: 0.75rem;
    font-size: 0.875rem;

    @include respond-to(sm) {
      width: auto;
      min-width: 120px;
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
    }

    @include respond-to(md) {
      padding: 1rem 2rem;
    }
  }
}

// ==============================|| RESPONSIVE MODALS ||============================== //

.responsive-modal {
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);

    @include respond-to(sm) {
      margin: 1rem auto;
      max-width: 500px;
    }

    @include respond-to(md) {
      margin: 2rem auto;
      max-width: 600px;
    }

    @include respond-to(lg) {
      max-width: 800px;
    }
  }

  .modal-content {
    border-radius: 0.5rem;

    @include respond-to(sm) {
      border-radius: 0.75rem;
    }

    @include respond-to(md) {
      border-radius: 1rem;
    }
  }

  .modal-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;

    @include respond-to(sm) {
      padding: 1.25rem;
    }

    @include respond-to(md) {
      padding: 1.5rem;
    }

    .modal-title {
      font-size: 1.125rem;
      font-weight: 600;

      @include respond-to(sm) {
        font-size: 1.25rem;
      }

      @include respond-to(md) {
        font-size: 1.5rem;
      }
    }
  }

  .modal-body {
    padding: 1rem;

    @include respond-to(sm) {
      padding: 1.25rem;
    }

    @include respond-to(md) {
      padding: 1.5rem;
    }
  }

  .modal-footer {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column-reverse;
    gap: 0.5rem;

    @include respond-to(sm) {
      padding: 1.25rem;
      flex-direction: row;
      justify-content: flex-end;
      gap: 1rem;
    }

    @include respond-to(md) {
      padding: 1.5rem;
    }

    .btn {
      width: 100%;

      @include respond-to(sm) {
        width: auto;
        min-width: 100px;
      }
    }
  }
}

// ==============================|| RESPONSIVE NAVIGATION ||============================== //

.responsive-nav {
  .nav-item {
    margin-bottom: 0.5rem;

    @include respond-to(md) {
      margin-bottom: 0;
      margin-right: 1rem;
    }
  }

  .nav-link {
    padding: 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;

    @include respond-to(md) {
      padding: 0.5rem 1rem;
      font-size: 1rem;
    }
  }
}

// ==============================|| RESPONSIVE UTILITIES ||============================== //

// Display utilities
.d-mobile-none {
  @include respond-below(md) {
    display: none !important;
  }
}

.d-desktop-none {
  @include respond-to(md) {
    display: none !important;
  }
}

.d-tablet-none {
  @include respond-below(lg) {
    @include respond-to(sm) {
      display: none !important;
    }
  }
}

// Text alignment utilities
.text-mobile-center {
  @include respond-below(md) {
    text-align: center !important;
  }
}

.text-desktop-left {
  @include respond-to(md) {
    text-align: left !important;
  }
}

// Width utilities
.w-mobile-full {
  @include respond-below(md) {
    width: 100% !important;
  }
}

.w-desktop-auto {
  @include respond-to(md) {
    width: auto !important;
  }
}

// Overflow utilities
.overflow-mobile-scroll {
  @include respond-below(md) {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
  }
}

// ==============================|| RESPONSIVE ANIMATIONS ||============================== //

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
